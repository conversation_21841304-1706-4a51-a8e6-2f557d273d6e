package com.kikitrade.kevent.server;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;


/**
 *
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = "com.kikitrade.kevent")
@EnableAsync(proxyTargetClass = true)
@EnableDubbo
public class KEventServerApplication extends SpringBootServletInitializer implements AsyncConfigurer {

    public static void main(String[] args) {
        SpringApplication.run(KEventServerApplication.class, args);
    }

    @Override
    public Executor getAsyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(16);
        executor.setMaxPoolSize(200);
        executor.setQueueCapacity(1000);
        executor.setThreadNamePrefix("KEventServerApplication-");
        executor.initialize();
        return executor;
    }

    @Override
    public AsyncUncaughtExceptionHandler getAsyncUncaughtExceptionHandler() {
        return null;
    }
}
