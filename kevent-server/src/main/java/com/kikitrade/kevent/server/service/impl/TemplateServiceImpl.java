package com.kikitrade.kevent.server.service.impl;

import com.kikitrade.kevent.server.service.TemplateService;
import com.kikitrade.kevent.server.service.TemplateTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.ExpressionContext;
import org.thymeleaf.spring6.expression.ThymeleafEvaluationContext;

import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

/**
 * 模板服务实现类
 *
 * <AUTHOR>
 * @create 2022/6/7 10:00 上午
 * @modify
 */
@Slf4j
@Service
public class TemplateServiceImpl implements TemplateService, ApplicationContextAware {

    @Resource
    TemplateEngine templateEngine;

    private ApplicationContext applicationContext;

    @Override
    public String render(TemplateTask templateTask) {
        Map<String, Object> parameterMap = new HashMap<>();
        parameterMap.putAll(templateTask.getParameterMap());
        final ThymeleafEvaluationContext evaluationContext =
                new ThymeleafEvaluationContext(applicationContext, null);
        parameterMap.put(ThymeleafEvaluationContext.THYMELEAF_EVALUATION_CONTEXT_CONTEXT_VARIABLE_NAME, evaluationContext);
        ExpressionContext expressionContext = new ExpressionContext(templateEngine.getConfiguration(), Locale.UK, parameterMap);
        return templateEngine.process(templateTask.getTemplateName(), expressionContext);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

}
