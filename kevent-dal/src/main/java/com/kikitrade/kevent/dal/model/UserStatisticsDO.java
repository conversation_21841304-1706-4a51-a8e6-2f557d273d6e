package com.kikitrade.kevent.dal.model;

import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.Table;
import lombok.Data;

import java.math.BigDecimal;

import static com.kikitrade.kevent.dal.UserStatisticsConstants.*;
import static com.kikitrade.kevent.dal.model.StatField.Type.*;

/**
 * 用户日维度统计数据
 *
 * <AUTHOR>
 * @create 2022/6/8 4:49 下午
 * @modify
 */
@Data
@Table(name = "user_statistics")
public class UserStatisticsDO {

    /**
     * 用户id
     */
    @PartitionKey(name = FIELD_CUSTOMER_ID)
    @StatField(name = FIELD_CUSTOMER_ID, include = true, type = STRING)
    private String customerId;
    /**
     * 统计日期
     */
    @PartitionKey(name = FIELD_AC_STATIC_DATE, value = 1)
    @StatField(name = FIELD_AC_STATIC_DATE, include = true, type = STRING)
    private String acStaticDate;

    /**
     * 今日用户总资产
     */
    @Column(name = FIELD_AC_AUM, type = Column.Type.STRING, isDefined = true)
    @StatField(name = FIELD_AC_AUM, include = true, type = DECIMAL)
    private BigDecimal acAum;
    /**
     * 今日理财资产
     */
    @Column(name = FIELD_AC_FINANCE_AUM, type = Column.Type.STRING, isDefined = true)
    @StatField(name = FIELD_AC_FINANCE_AUM, type = DECIMAL)
    private BigDecimal acFinanceAum;
    /**
     * 今日现货资产
     */
    @Column(name = FIELD_AC_SPOT_AUM, type = Column.Type.STRING, isDefined = true)
    @StatField(name = FIELD_AC_SPOT_AUM, type = DECIMAL)
    private BigDecimal acSpotAum;
    /**
     * 今日c2c资产
     */
    @Column(name = FIELD_AC_C2C_AUM, type = Column.Type.STRING, isDefined = true)
    @StatField(name = FIELD_AC_C2C_AUM, type = DECIMAL)
    private BigDecimal acC2cAum;
    /**
     * 昨日用户总资产
     */
    @Column(name = FIELD_AC_YESTERDAY_AUM, type = Column.Type.STRING, isDefined = true)
    @StatField(name = FIELD_AC_YESTERDAY_AUM, include = true, type = DECIMAL)
    private BigDecimal acYesterdayAum;
    /**
     * 昨日理财资产
     */
    @Column(name = FIELD_AC_YESTERDAY_FINANCE_AUM, type = Column.Type.STRING, isDefined = true)
    @StatField(name = FIELD_AC_YESTERDAY_FINANCE_AUM, type = DECIMAL)
    private BigDecimal acYesterdayFinanceAum;
    /**
     * 昨日现货资产
     */
    @Column(name = FIELD_AC_YESTERDAY_SPOT_AUM, type = Column.Type.STRING, isDefined = true)
    @StatField(name = FIELD_AC_YESTERDAY_SPOT_AUM, type = DECIMAL)
    private BigDecimal acYesterdaySpotAum;
    /**
     * 昨日c2c资产
     */
    @Column(name = FIELD_AC_YESTERDAY_C2C_AUM, type = Column.Type.STRING, isDefined = true)
    @StatField(name = FIELD_AC_YESTERDAY_C2C_AUM, type = DECIMAL)
    private BigDecimal acYesterdayC2cAum;
    /**
     * 截止前一日累计入金_USD
     */
    @Column(name = FIELD_AC_ACCUMULATED_AMOUNT_DEPOSIT, type = Column.Type.STRING, isDefined = true)
    @StatField(name = FIELD_AC_ACCUMULATED_AMOUNT_DEPOSIT, include = true, type = DECIMAL)
    private BigDecimal acAccumulatedAmountDeposit;
    /**
     * 截止前一日累计出金_USD
     */
    @Column(name = FIELD_AC_ACCUMULATED_AMOUNT_WITHDRAW, type = Column.Type.STRING, isDefined = true)
    @StatField(name = FIELD_AC_ACCUMULATED_AMOUNT_WITHDRAW, include = true, type = DECIMAL)
    private BigDecimal acAccumulatedAmountWithdraw;
    /**
     * 截止前一日法币累计入金_USD
     */
    @Column(name = FIELD_AC_FIAT_ACCUMULATED_AMOUNT_DEPOSIT, type = Column.Type.STRING, isDefined = true)
    @StatField(name = FIELD_AC_FIAT_ACCUMULATED_AMOUNT_DEPOSIT, include = false, type = DECIMAL)
    private BigDecimal acFiatAccumulatedAmountDeposit;
    /**
     * 截止前一日法币累计出金_USD
     */
    @Column(name = FIELD_AC_FIAT_ACCUMULATED_AMOUNT_WITHDRAW, type = Column.Type.STRING, isDefined = true)
    @StatField(name = FIELD_AC_FIAT_ACCUMULATED_AMOUNT_WITHDRAW, include = false, type = DECIMAL)
    private BigDecimal acFiatAccumulatedAmountWithdraw;
    /**
     * 截止前一日crypto累计入金_USD
     */
    @Column(name = FIELD_AC_CRYPTO_ACCUMULATED_AMOUNT_DEPOSIT, type = Column.Type.STRING, isDefined = true)
    @StatField(name = FIELD_AC_CRYPTO_ACCUMULATED_AMOUNT_DEPOSIT, include = false, type = DECIMAL)
    private BigDecimal acCryptoAccumulatedAmountDeposit;
    /**
     * 截止前一日crypto累计出金_USD
     */
    @Column(name = FIELD_AC_CRYPTO_ACCUMULATED_AMOUNT_WITHDRAW, type = Column.Type.STRING, isDefined = true)
    @StatField(name = FIELD_AC_CRYPTO_ACCUMULATED_AMOUNT_WITHDRAW, include = false, type = DECIMAL)
    private BigDecimal acCryptoAccumulatedAmountWithdraw;
    /**
     * 截止前一日c2c累计入金_USD
     */
    @Column(name = FIELD_AC_C2C_ACCUMULATED_AMOUNT_DEPOSIT, type = Column.Type.STRING, isDefined = true)
    @StatField(name = FIELD_AC_C2C_ACCUMULATED_AMOUNT_DEPOSIT, include = false, type = DECIMAL)
    private BigDecimal acC2cAccumulatedAmountDeposit;

    /**
     * 截止前一日c2c累计出金_USD
     */
    @Column(name = FIELD_AC_C2C_ACCUMULATED_AMOUNT_WITHDRAW, type = Column.Type.STRING, isDefined = true)
    @StatField(name = FIELD_AC_C2C_ACCUMULATED_AMOUNT_WITHDRAW, include = false, type = DECIMAL)
    private BigDecimal acC2cAccumulatedAmountWithdraw;

    /**
     * 截止前一日circle累计入金_USD
     */
    @Column(name = FIELD_AC_CIRCLE_ACCUMULATED_AMOUNT_DEPOSIT, type = Column.Type.STRING, isDefined = true)
    @StatField(name = FIELD_AC_CIRCLE_ACCUMULATED_AMOUNT_DEPOSIT, include = false, type = DECIMAL)
    private BigDecimal acCircleAccumulatedAmountDeposit;

    /**
     * 截止前一日circle累计出金_USD
     */
    @Column(name = FIELD_AC_CIRCLE_ACCUMULATED_AMOUNT_WITHDRAW, type = Column.Type.STRING, isDefined = true)
    @StatField(name = FIELD_AC_CIRCLE_ACCUMULATED_AMOUNT_WITHDRAW, include = false, type = DECIMAL)
    private BigDecimal acCircleAccumulatedAmountWithdraw;

    /**
     * 截止前一日累计申请入金_USD
     */
    @Column(name = FIELD_AC_ACCUMULATED_AMOUNT_DEPOSIT_APPLY, type = Column.Type.STRING, isDefined = true)
    @StatField(name = FIELD_AC_ACCUMULATED_AMOUNT_DEPOSIT_APPLY, include = true, type = DECIMAL)
    private BigDecimal acAccumulatedAmountDepositApply;

    /**
     * 截止前一日累计申请出金_USD
     */
    @Column(name = FIELD_AC_ACCUMULATED_AMOUNT_WITHDRAW_APPLY, type = Column.Type.STRING, isDefined = true)
    @StatField(name = FIELD_AC_ACCUMULATED_AMOUNT_WITHDRAW_APPLY, include = true, type = DECIMAL)
    private BigDecimal acAccumulatedAmountWithdrawApply;

    /**
     * 用户pnl
     */
    @Column(name = FIELD_AC_PNL, type = Column.Type.STRING)
    @StatField(name = FIELD_AC_PNL, include = true, type = DECIMAL)
    private BigDecimal acPnl;

    /**
     * 近30天用户日最大挂单数
     */
    @Column(name = FIELD_AC_30DAYS_MAX_ORDER_CNT, type = Column.Type.INTEGER)
    @StatField(name = FIELD_AC_30DAYS_MAX_ORDER_CNT, include = true, type = DECIMAL)
    private Long ac30daysMaxOrderCnt;

    /**
     * 近30天用户日最大取消挂单数
     */
    @Column(name = FIELD_AC_30DAYS_MAX_CANCELLED_ORDER_CNT, type = Column.Type.INTEGER)
    @StatField(name = FIELD_AC_30DAYS_MAX_CANCELLED_ORDER_CNT, include = true, type = DECIMAL)
    private Long ac30daysMaxCancelledOrderCnt;

    /**
     * 近30天用户日最大成交单数
     */
    @Column(name = FIELD_AC_30DAYS_MAX_FILLED_ORDER_CNT, type = Column.Type.INTEGER)
    @StatField(name = FIELD_AC_30DAYS_MAX_FILLED_ORDER_CNT, include = true, type = INTEGER)
    private Long ac30daysMaxFilledOrderCnt;

    /**
     * 过去7天内用户没有修改过密码
     */
    @Column(name = FIELD_AC_REVISE_PASSWORD, type = Column.Type.INTEGER)
    @StatField(name = FIELD_AC_REVISE_PASSWORD, include = true, type = INTEGER)
    private Long acRevisePassword;

    /**
     * 过去7天内用户没有修改过2FA
     */
    @Column(name = FIELD_AC_REVISE_2FA, type = Column.Type.INTEGER)
    @StatField(name = FIELD_AC_REVISE_2FA, include = true, type = INTEGER)
    private Long acRevise2FA;

    /**
     * 过去7天内用户没有修改过Email
     */
    @Column(name = FIELD_AC_REVISE_EMAIL, type = Column.Type.INTEGER)
    @StatField(name = FIELD_AC_REVISE_EMAIL, include = true, type = INTEGER)
    private Long acReviseEmail;

    /**
     * 用户修改过Phone
     * 修改1，未修改0
     */
    @Column(name = FIELD_AC_REVISE_PHONE, type = Column.Type.INTEGER)
    @StatField(name = FIELD_AC_REVISE_PHONE, include = true, type = INTEGER)
    private Long acRevisePhone;

    /**
     * 用户本次登录设备id不同于前7日
     * 不一致1，一致0
     */
    @Column(name = FIELD_AC_REVISE_DEVICE_ID, type = Column.Type.INTEGER)
    @StatField(name = FIELD_AC_REVISE_DEVICE_ID, include = true, type = INTEGER)
    private Long acReviseDeviceId;
}
