package com.kikitrade.kevent.server.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.kikitrade.kevent.dal.builder.EventStoreBuilder;
import com.kikitrade.kevent.dal.model.EventDO;
import com.kikitrade.kevent.server.executor.ExecutorManager;
import com.kikitrade.kevent.server.service.EventService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


import jakarta.annotation.Resource;

/**
 * @author: penuel
 * @date: 2022/5/5 09:37
 * @desc: TODO
 */
@Service
@Slf4j
public class EventServiceImpl implements EventService {

    @Autowired
    private EventStoreBuilder eventStoreBuilder;
    @Resource
    private ExecutorManager executorManager;


    @Override
    public boolean process(EventDO event) {
        log.info("EventServiceImpl process event{}", JSONObject.toJSONString(event));
        boolean save = eventStoreBuilder.save(event);
        if (!save) {
            return false;
        }
        return executorManager.process(event);
    }
}