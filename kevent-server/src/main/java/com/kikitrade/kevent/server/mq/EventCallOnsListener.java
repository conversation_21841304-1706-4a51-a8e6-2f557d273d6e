package com.kikitrade.kevent.server.mq;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.kikitrade.framework.ons.OnsMessageListener;
import com.kikitrade.framework.ons.OnsProperties;
import com.kikitrade.kevent.common.constant.EventConstants;
import com.kikitrade.kevent.common.model.EventCallDTO;
import com.kikitrade.kevent.common.model.EventDTO;
import com.kikitrade.kevent.dal.builder.EventStoreBuilder;
import com.kikitrade.kevent.dal.model.EventDO;
import com.kikitrade.kevent.server.autoconfigure.KEventProperties;
import com.kikitrade.kevent.server.channel.ActivityTaskTrackingExecutor;
import com.kikitrade.kevent.server.service.EventService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/5/21 11:42
 */
@Component
@Slf4j
public class EventCallOnsListener implements OnsMessageListener {

    @Autowired
    private KEventProperties kEventProperties;
    @Resource
    private OnsProperties onsProperties;
    @Autowired
    public EventStoreBuilder eventStoreBuilder;
    @Autowired
    private ActivityTaskTrackingExecutor activityTaskTrackingExecutor;

    @Override
    public String topic() {
        return kEventProperties.getOnsTopicEventCall();
    }

    @Override
    public boolean traffic() {
        return null != onsProperties && onsProperties.isEnableTraffic();
    }

    @Override
    public Action consume(Message message, ConsumeContext context) {
        log.info("consume message:{}", JSONObject.toJSONString(message));
        String eventMessage = new String(message.getBody());
        try {
            EventCallDTO event = JSONObject.parseObject(eventMessage, EventCallDTO.class);
            List<EventDO> eventDOS = eventStoreBuilder.filterEventByCustomerId(event.getCustomerId(), EventConstants.DeliveryChannel.ACTIVITY, EventConstants.DeliveryStatus.WAIT_CALL, EventConstants.EventName.getByName(event.getName()));
            if(CollectionUtils.isNotEmpty(eventDOS)){
                EventDO eventDO = eventDOS.get(0);
                eventDO.setCallEvent(event.getCallEvent());
                activityTaskTrackingExecutor.execute(eventDO);
            }
        }catch (Exception ex){
            log.error("EventCallOnsListener:{}", eventMessage ,ex);
        }
        return Action.CommitMessage;
    }
}
