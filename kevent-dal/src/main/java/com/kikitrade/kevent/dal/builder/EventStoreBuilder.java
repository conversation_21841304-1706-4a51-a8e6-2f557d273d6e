package com.kikitrade.kevent.dal.builder;

import com.kikitrade.framework.common.model.TokenPage;
import com.kikitrade.kevent.common.constant.EventConstants;
import com.kikitrade.kevent.dal.model.EventDO;

import java.util.List;

/**
 * @author: penuel
 * @date: 2022/5/5 09:38
 * @desc: TODO
 */
public interface EventStoreBuilder {

    /**
     * 保存
     * @param event
     * @return
     */
    boolean save(EventDO event);

    /**
     * 根据不同渠道 更新投递状态
     * @param eventId
     * @param deliveryChannel
     * @param deliveryStatus
     * @return
     */
    boolean updateStatusByType(String eventId, EventConstants.DeliveryChannel deliveryChannel, EventConstants.DeliveryStatus deliveryStatus);

    /**
     * 查询投递状态
     * @param eventId
     * @param deliveryChannel
     * @return
     */
    EventConstants.DeliveryStatus getDeliveryStatusByType(String eventId, EventConstants.DeliveryChannel deliveryChannel);

    /**
     * 按时间获取时间范围内的事件
     * @param start
     * @param end
     * @return
     */
    TokenPage<EventDO> listEventByTime(Long start, Long end, EventConstants.DeliveryChannel deliveryChannel, EventConstants.DeliveryStatus deliveryStatus, String nextToken, int limit);

    /**
     * 按时间获取时间范围内的指定事件
     * @param start
     * @param end
     * @param deliveryChannel
     * @param deliveryStatus
     * @param eventNameList
     * @param nextToken
     * @param limit
     * @return
     */
    TokenPage<EventDO> filterEventByTime(Long start, Long end, EventConstants.DeliveryChannel deliveryChannel, EventConstants.DeliveryStatus deliveryStatus, List<EventConstants.EventName> eventNameList, String nextToken, int limit);

    /**
     * 按创建时间获取时间范围内的事件
     * @param end
     * @param deliveryChannel
     * @param deliveryStatus
     * @param eventNameList
     * @param nextToken
     * @param limit
     * @return
     */
    TokenPage<EventDO> filterEventByCreated(Long end, EventConstants.DeliveryChannel deliveryChannel, List<EventConstants.DeliveryStatus> deliveryStatus, List<EventConstants.EventName> eventNameList, String nextToken, int limit);

    /**
     * 获取某个用户某个事件
     * @param customerId
     * @param deliveryChannel
     * @param deliveryStatus
     * @param eventName
     * @return
     */
    List<EventDO> filterEventByCustomerId(String customerId, EventConstants.DeliveryChannel deliveryChannel, EventConstants.DeliveryStatus deliveryStatus, EventConstants.EventName eventName);

    /**
     * 删除事件
     * @param eventIds
     * @return
     */
    boolean delete(List<String> eventIds);
}
