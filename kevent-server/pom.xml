<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.kikitrade</groupId>
        <artifactId>kevent</artifactId>
        <version>2.0.2-SNAPSHOT</version>
    </parent>

    <artifactId>kevent-server</artifactId>
    <name>kevent-server</name>
    <description>Demo project for Spring Boot</description>
    <properties>
        <project.property.path>..</project.property.path>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.kikitrade</groupId>
            <artifactId>kevent-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kikitrade</groupId>
            <artifactId>kevent-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kikitrade</groupId>
            <artifactId>kevent-dal</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure-processor</artifactId>
        </dependency>

        <dependency>
            <groupId>com.kikitrade</groupId>
            <artifactId>kiki-redis-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kikitrade</groupId>
            <artifactId>kiki-ons-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-x-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jboss.netty</groupId>
            <artifactId>netty</artifactId>
        </dependency>
        <dependency>
            <groupId>org.thymeleaf</groupId>
            <artifactId>thymeleaf</artifactId>
        </dependency>
        <dependency>
            <groupId>org.thymeleaf</groupId>
            <artifactId>thymeleaf-spring6</artifactId>
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-saf</artifactId>
            <version>3.0.1</version>
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-core</artifactId>
            <optional>true</optional>
            <version>4.5.1</version>
        </dependency>
        <!--  elastic job -->
        <dependency>
            <groupId>com.kikitrade</groupId>
            <artifactId>kiki-elasticjob-spring-boot-starter</artifactId>
        </dependency>

        <!-- zookeeper client-->
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-framework</artifactId>
        </dependency>

        <dependency>
            <groupId>com.kikitrade</groupId>
            <artifactId>kiki-observability-metrics-spring-boot-starter</artifactId>
        </dependency>
        <!--   nacos     -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-commons</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-context</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kikitrade</groupId>
            <artifactId>kiki-observability-tracing-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kikitrade</groupId>
            <artifactId>kiki-dubbo-deployment-tag-aware-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.kikitrade</groupId>
            <artifactId>kmarket-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>dubbo</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.kikitrade</groupId>
            <artifactId>kmarket-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>dubbo</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <build>
        <finalName>kevent-server</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <!--<plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>-->
        </plugins>
    </build>

</project>
