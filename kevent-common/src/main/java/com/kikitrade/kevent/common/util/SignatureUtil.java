package com.kikitrade.kevent.common.util;

import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Map;
import java.util.TreeMap;

public class SignatureUtil {

    public static String generateSignature(TreeMap<String, Object> sortedMap, String secretKey) {
        sortedMap.remove("signature");
        // 签名字符串
        StringBuilder signStr = new StringBuilder();
        for (Map.Entry<String, Object> entry : sortedMap.entrySet()) {
            Object value = entry.getValue();
            String key = entry.getKey();
            if (value != null && !"".equals(value)) {
                //空值不传递，不参与签名组串
                signStr.append(key + "=" + value + "&");
            }
        }
        System.out.println(signStr);
        // 添加商户密钥生成最终签名
        String sign = signStr.toString() + secretKey;
        return md5(sign);
    }

    private static String md5(String str) {
        try {
            MessageDigest digest = MessageDigest.getInstance("MD5");
            digest.update(str.getBytes());
            return new BigInteger(1, digest.digest()).toString(16);
        } catch (NoSuchAlgorithmException e) {
            return "";
        }
    }
}
