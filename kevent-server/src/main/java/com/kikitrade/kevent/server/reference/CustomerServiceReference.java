package com.kikitrade.kevent.server.reference;

import com.kikitrade.kcustomer.api.model.DeviceInfoDTO;
import com.kikitrade.kcustomer.api.service.RemoteDeviceInfoService;
import com.kikitrade.kevent.server.autoconfigure.KEventProperties;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;


@Slf4j
@Service
public class CustomerServiceReference {

    @DubboReference(timeout = 10000, check = false)
    private RemoteDeviceInfoService remoteDeviceInfoService;

    @Resource
    private KEventProperties eventProperties;

    /**
     * 根据用户ID查询最新设备信息
     */
    public DeviceInfoDTO getDeviceInfoByCustomerId(String customerId) {
        try {
            return remoteDeviceInfoService.getLastByCustomerId(eventProperties.getSaasId(), customerId);
        } catch (Exception e) {
            log.error("CustomerServiceReference getLastByCustomerId rpc error,customerId:{}", customerId, e);
        }
        return null;
    }

    /**
     * 根据设备ID查询设备信息
     */
    public DeviceInfoDTO getDeviceInfoByDeviceId(String deviceId) {
        try {
            return remoteDeviceInfoService.getByDeviceId(eventProperties.getSaasId(), deviceId);
        } catch (Exception e) {
            log.error("CustomerServiceReference getDeviceInfoByDeviceId rpc error,deviceId:{}", deviceId, e);
        }
        return null;
    }
}
