//package com.kikitrade.kevent.server.executor.impl;
//
//import com.alibaba.fastjson.JSONObject;
//import com.kikitrade.kevent.common.constant.EventConstants;
//import com.kikitrade.kevent.dal.model.EventDO;
//import com.kikitrade.kevent.server.autoconfigure.KEventProperties;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//import org.springframework.util.MultiValueMap;
//import org.springframework.web.client.RestTemplate;
//
//import jakarta.annotation.Resource;
//
///**
// * @author: penuel
// * @date: 2022/5/5 15:46
// * @desc: TODO
// */
//@Service
//public class CommonTrackingExecutor extends TrackingExecutor {
//
//    @Autowired
//    private RestTemplate restTemplate;
//
//    @Override
//    public EventConstants.DeliveryChannel deliveryChannel() {
//        throw new IllegalArgumentException("deliveryChannel is null");
//    }
//
//    @Override
//    public MultiValueMap<String, Object> mapping(EventDO event) {
//        throw new IllegalArgumentException("track is not implemented");
//    }
//
//    @Override
//    public boolean track(MultiValueMap<String, Object> params) {
//        throw new IllegalArgumentException("track is not implemented");
//    }
//
//    @Override
//    public String endpoint() {
//        throw new IllegalArgumentException("endpoint is null");
//    }
//}
