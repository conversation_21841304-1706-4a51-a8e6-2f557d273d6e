/**
 * Copyright:   北京互融时代软件有限公司
 *
 * @author: <PERSON>
 * @version: V1.0
 * @Date: 2015年9月16日 上午11:04:39
 */
package com.kikitrade.kevent.common.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

public class DateUtil {
    public static final String yyyy_mm_dd_hh_mm_ss_sss = "yyyy-mm-dd hh:mm:ss.sss";

    /**
     * 比较两个时间相差多少分钟
     * <p> TODO</p>
     *
     * @author: <PERSON>
     * @param: @param date1  较大时间
     * @param: @param date2  较小时间
     * @param: @return
     * @return: int
     * @Date :          2016年4月20日 上午10:29:49
     * @throws:
     */
    public static int compareDateMinute(Date date1, Date date2) {
        Calendar dateOne = Calendar.getInstance();
        dateOne.setTime(date1);    //设置date1
        Calendar dateTwo = Calendar.getInstance();
        dateTwo.setTime(date2);    //设置date2
        long timeOne = dateOne.getTimeInMillis();
        long timeTwo = dateTwo.getTimeInMillis();
        long minute = (timeOne - timeTwo) / (1000 * 60);//转化minute
        return Long.valueOf(minute).intValue();
    }

    /**
     * 比较两个时间相差多少个space分钟
     * <p> TODO</p>
     *
     * @author: Liu Shilei
     * @param: @param date1  较大时间
     * @param: @param date2  较小时间
     * @param: @param space  时间间隔
     * @param: @return
     * @return: int
     * @Date :          2016年4月20日 上午10:29:49
     * @throws:
     */
    public static int compareDateMinuteSpace(Date date1, Date date2, int space) {
        Calendar dateOne = Calendar.getInstance();
        dateOne.setTime(date1);    //设置date1
        Calendar dateTwo = Calendar.getInstance();
        dateTwo.setTime(date2);    //设置date2
        long timeOne = dateOne.getTimeInMillis();
        long timeTwo = dateTwo.getTimeInMillis();
        long minute = (timeOne - timeTwo) / (1000 * 60 * space);//转化minute
        return Long.valueOf(minute).intValue();
    }
    /**
     * 在日期上加分钟数，得到新的日期
     *
     * @return
     */
    public final static Date addMinToDate(Date date, int min) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.MINUTE, min);
        return c.getTime();
    }

    /**
     * 在日期上加小时数，得到新的日期
     *
     * @return
     */
    public final static Date addHourToDate(Date date, int hour) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.HOUR, hour);
        return c.getTime();
    }

    /**
     * 在日期上加天数，得到新的日期
     *
     * @return
     */
    public final static Date addDayToDate(Date date, int day) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.DATE, day);
        return c.getTime();
    }

    public static String date2UTC(Long date) {
        SimpleDateFormat sbf = new SimpleDateFormat(yyyy_mm_dd_hh_mm_ss_sss);
        return sbf.format(date);
    }


    public static void main(String[] args) throws ParseException {
        long time = (System.currentTimeMillis() - 1653996600127L);
        System.out.println(compareDateMinuteSpace(new Date(),new Date(1653996600127L),10));
    }

}
