package com.kikitrade.kevent.common.constant;

import lombok.Getter;

import java.util.Arrays;

/**
 * 阿里云风控常量
 *
 * <AUTHOR>
 * @create 2022/6/23 11:07 上午
 * @modify
 */
public interface RiskConstants {

    String EVENT_TPL = "Event.tpl";

    enum RiskRequestParam {
        ACTION("RequestDecision"),
        SERVICE_PARAMETERS("ServiceParameters"),
        REGION_ID("RegionId");

        private String key;

        RiskRequestParam(String key) {
            this.key = key;
        }

        public String key() {
            return this.key;
        }
    }

    // 风控返回字段
    enum RiskReplyParam {
        CODE("Code"),
        DATA("Data"),
        DATA_SCORE("score"),
        DATA_FINAL_DECISION("finalDecision"),
        DATA_TAGS("tags"),
        EXTEND("extend"),
        ;

        private String key;

        RiskReplyParam(String key) {
            this.key = key;
        }

        public String key() {
            return this.key;
        }
    }

    enum RiskReportParam {
        // Business customer id
        CUSTOMER_ID("customerId"),
        EVENT_CODE("eventCode"),
        TIME("time"),
        UID("uid"),
        CHANNEL("channel"),
        TYPE("type"),
        STATUS("status"),
        PRICE("price"),
        VALUE("value"),
        BODY("body"),
        TEMPLATE_NAME("templateName"),
        DEVICE_TOKEN("deviceToken"),
        IP("ip"),
        DEVICE_ID("deviceId"),
        ENV("env"),
        ;

        private String key;

        RiskReportParam(String key) {
            this.key = key;
        }

        public String key() {
            return this.key;
        }
    }

    enum Template {
        Pay,
        Trade,
        PayV1,
        TradeV1,
        Statistics,
        RedPocket,
        Registration,
        Login,
        Verify,
        Unblock,
        ;

        public String templateName() {
            return name() + EVENT_TPL;
        }
    }

    enum RiskChannel {
        CRYPTO("crypto"),
        C2C("c2c"),
        CIRCLE("circle"),
        RED_POCKET("RED_POCKET"),
        ;

        private String key;

        RiskChannel(String key) {
            this.key = key;
        }

        public String key() {
            return this.key;
        }
    }
    enum RiskType {
        DEPOSIT("deposit"),
        WITHDRAW("withdraw"),
        RELEASE("release"),
        RECEIVE("receive"),
        ;

        private String key;

        RiskType(String key) {
            this.key = key;
        }

        public String key() {
            return this.key;
        }
    }

    enum RiskStatus {
        APPLY("apply"),
        SUCCESS("success"),
        CANCEL("cancel"),
        REJECT("reject")
        ;

        private String key;

        RiskStatus(String key) {
            this.key = key;
        }

        public String key() {
            return this.key;
        }
    }

    @Getter
    enum RiskEvent {

        CRYPTO_DEPOSIT(EventConstants.EventName.CRYPTO_DEPOSIT, RiskChannel.CRYPTO, RiskType.DEPOSIT, RiskStatus.SUCCESS),
        C2C_DEPOSIT(EventConstants.EventName.C2C_DEPOSIT, RiskChannel.C2C, RiskType.DEPOSIT, RiskStatus.SUCCESS),
        CIRCLE_DEPOSIT(EventConstants.EventName.CIRCLE_DEPOSIT, RiskChannel.CIRCLE, RiskType.DEPOSIT, RiskStatus.SUCCESS),
        C2C_DEPOSIT_APPLY(EventConstants.EventName.C2C_DEPOSIT_APPLY, RiskChannel.C2C, RiskType.DEPOSIT, RiskStatus.APPLY),
        C2C_DEPOSIT_CANCEL(EventConstants.EventName.C2C_DEPOSIT_CANCEL, RiskChannel.C2C, RiskType.DEPOSIT, RiskStatus.CANCEL),
        CRYPTO_WITHDRAW_APPLY(EventConstants.EventName.CRYPTO_WITHDRAW_APPLY, RiskChannel.CRYPTO, RiskType.WITHDRAW, RiskStatus.APPLY),
        C2C_WITHDRAW_APPLY(EventConstants.EventName.C2C_WITHDRAW_APPLY, RiskChannel.C2C, RiskType.WITHDRAW, RiskStatus.APPLY),
        CIRCLE_WITHDRAW_APPLY(EventConstants.EventName.CIRCLE_WITHDRAW_APPLY, RiskChannel.CIRCLE, RiskType.WITHDRAW, RiskStatus.APPLY),
        CRYPTO_WITHDRAW(EventConstants.EventName.CRYPTO_WITHDRAW, RiskChannel.CRYPTO, RiskType.WITHDRAW, RiskStatus.SUCCESS),
        C2C_WITHDRAW(EventConstants.EventName.C2C_WITHDRAW, RiskChannel.C2C, RiskType.WITHDRAW, RiskStatus.SUCCESS),
        CIRCLE_WITHDRAW(EventConstants.EventName.CIRCLE_WITHDRAW, RiskChannel.CIRCLE, RiskType.WITHDRAW, RiskStatus.SUCCESS),
        CRYPTO_WITHDRAW_CANCEL(EventConstants.EventName.CRYPTO_WITHDRAW_CANCEL, RiskChannel.CRYPTO, RiskType.WITHDRAW, RiskStatus.CANCEL),
        C2C_WITHDRAW_CANCEL(EventConstants.EventName.C2C_WITHDRAW_CANCEL, RiskChannel.C2C, RiskType.WITHDRAW, RiskStatus.CANCEL),
        CIRCLE_WITHDRAW_CANCEL(EventConstants.EventName.CIRCLE_WITHDRAW_CANCEL, RiskChannel.CIRCLE, RiskType.WITHDRAW, RiskStatus.CANCEL),
        CRYPTO_WITHDRAW_REJECT(EventConstants.EventName.CRYPTO_WITHDRAW_REJECT, RiskChannel.CRYPTO, RiskType.WITHDRAW, RiskStatus.REJECT),
        CIRCLE_WITHDRAW_REJECT(EventConstants.EventName.CIRCLE_WITHDRAW_REJECT, RiskChannel.CIRCLE, RiskType.WITHDRAW, RiskStatus.REJECT),
        RELEASE_RED_POCKET(EventConstants.EventName.RELEASE_RED_POCKET, RiskChannel.RED_POCKET, RiskType.RELEASE, RiskStatus.SUCCESS),
        RECEIVE_RED_POCKET(EventConstants.EventName.RECEIVE_RED_POCKET, RiskChannel.RED_POCKET, RiskType.RECEIVE, RiskStatus.SUCCESS),
        ;

        RiskEvent(EventConstants.EventName originEvent, RiskConstants.RiskChannel channel, RiskConstants.RiskType type, RiskConstants.RiskStatus status) {
            this.originEvent = originEvent;
            this.channel = channel;
            this.type = type;
            this.status = status;
        }

        private EventConstants.EventName originEvent;
        private RiskConstants.RiskChannel channel;
        private RiskConstants.RiskType type;
        private RiskConstants.RiskStatus status;


        public static RiskEvent fromEventName(String eventName) {
            return Arrays.stream(RiskEvent.values()).filter(e -> e.getOriginEvent().getName().equalsIgnoreCase(eventName)).findFirst().orElse(null);
        }

    }

    @Getter
    enum RedPocketEvent {
        RELEASE_RED_POCKET(EventConstants.EventName.RELEASE_RED_POCKET, RedPocketAction.RELEASE),
        RECEIVE_RED_POCKET(EventConstants.EventName.RECEIVE_RED_POCKET, RedPocketAction.RECEIVE),
        ;
        private EventConstants.EventName eventName;
        private RiskConstants.RedPocketAction action;

        RedPocketEvent(EventConstants.EventName eventName, RiskConstants.RedPocketAction action) {
            this.eventName = eventName;
            this.action = action;
        }

        public static RedPocketEvent fromEventName(String eventName) {
            return Arrays.stream(RedPocketEvent.values()).filter(e -> e.getEventName().getName().equalsIgnoreCase(eventName)).findFirst().orElse(null);
        }
    }

    @Getter
    enum RedPocketAction{
        RELEASE("release"),
        RECEIVE("receive"),
        ;
        private String action;

        RedPocketAction(String action) {
            this.action = action;
        }
    }
}
