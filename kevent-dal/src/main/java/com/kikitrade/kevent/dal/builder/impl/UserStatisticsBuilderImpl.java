package com.kikitrade.kevent.dal.builder.impl;

import com.alicloud.openservices.tablestore.model.Direction;
import com.alicloud.openservices.tablestore.model.PrimaryKeyValue;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import com.kikitrade.framework.ots.mapping.RangeQueryParameter;
import com.kikitrade.kevent.dal.UserStatisticsConstants;
import com.kikitrade.kevent.dal.builder.UserStatisticsBuilder;
import com.kikitrade.kevent.dal.model.UserStatisticsDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;

import java.util.ArrayList;
import java.util.List;

/**
 * User statistics ots accessor implementation
 *
 * <AUTHOR>
 * @create 2022/6/8 5:14 下午
 * @modify
 */
@Component
@Slf4j
public class UserStatisticsBuilderImpl extends WideColumnStoreBuilder<UserStatisticsDO> implements UserStatisticsBuilder {

    @PostConstruct
    public void init() {
        init(UserStatisticsDO.class);
    }

    @Override
    public UserStatisticsDO get(String customerId, String acStaticDate) {
        UserStatisticsDO pk = new UserStatisticsDO();
        pk.setCustomerId(customerId);
        pk.setAcStaticDate(acStaticDate);
        return getRow(pk);
    }

    @Override
    public UserStatisticsDO getLatest(String customerId) {
        List<RangeQueryParameter> queryList = new ArrayList<>();
        queryList.add(new RangeQueryParameter(UserStatisticsConstants.FIELD_CUSTOMER_ID, PrimaryKeyValue.fromString(customerId), PrimaryKeyValue.fromString(customerId)));
        queryList.add(new RangeQueryParameter(UserStatisticsConstants.FIELD_AC_STATIC_DATE, PrimaryKeyValue.INF_MAX, PrimaryKeyValue.INF_MIN));
        return rangeQueryOne(queryList, Direction.BACKWARD);
    }

}
