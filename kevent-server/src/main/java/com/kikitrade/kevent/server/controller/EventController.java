package com.kikitrade.kevent.server.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kikitrade.framework.common.model.TokenPage;
import com.kikitrade.kevent.common.constant.EventConstants;
import com.kikitrade.kevent.dal.builder.EventStoreBuilder;
import com.kikitrade.kevent.dal.model.EventDO;
import com.kikitrade.kevent.server.channel.AdjustTrackingExecutor;
import com.kikitrade.kevent.server.channel.AliyunRiskTrackingExecutor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("event")
@Slf4j
public class EventController {

    @Autowired
    private EventStoreBuilder eventStoreBuilder;

    @Autowired(required = false)
    AdjustTrackingExecutor adjustTrackingExecutor;

    @Autowired(required = false)
    AliyunRiskTrackingExecutor aliyunRiskTrackingExecutor;
    /**
     * adjust数据修复
     */
    @GetMapping("/data/fix")
    public String dataFix(@RequestParam Long dateStart,
                            @RequestParam Long dateEnd) {
        JSONObject jsonObject = new JSONObject();
        List<String>  EventFailIds = new ArrayList<>();
        try {
            TokenPage<EventDO> eventDOTokenPage = null;
            String nextToken = "";
            while (true){
                eventDOTokenPage = eventStoreBuilder.listEventByTime(dateStart,dateEnd, EventConstants.DeliveryChannel.ADJUST, EventConstants.DeliveryStatus.FAIL,nextToken,100);
                if (eventDOTokenPage == null || eventDOTokenPage.getRows() == null || eventDOTokenPage.getRows().size() <=0 ){
                    break;
                }
                eventDOTokenPage.getRows().forEach(event -> {
                    if(adjustTrackingExecutor.isContinue(event)){
                        //异步执行
                        EventConstants.DeliveryStatus deliveryStatus = adjustTrackingExecutor.executeFix((EventDO) event);
                        log.info("dataFix:{}", JSON.toJSONString(event),"deliveryStatus:{}",deliveryStatus.name());
                        if (deliveryStatus.equals(EventConstants.DeliveryStatus.FAIL)){
                            EventFailIds.add((event).getId());
                        }
                    }
                });
                nextToken = eventDOTokenPage.getNextToken();
                if (nextToken == null){
                    break;
                }
            }
        }catch (Exception e){
            log.error("dataFix error",e);
            jsonObject.put("fail",e.getMessage());
        }
        jsonObject.put("success","OK");
        jsonObject.put("EventFailIds",EventFailIds);
        return JSON.toJSONString(jsonObject);
    }

    @PostMapping("/test/risk")
    public String reportRisk(@RequestBody EventDO eventDO){
        log.info("test reportRisk {}", eventDO);
        try {
            EventConstants.DeliveryStatus track = aliyunRiskTrackingExecutor.doExecute(eventDO);
            log.info("test reportRisk event {}, track {}", eventDO, track);
            return "success";
        } catch (Exception e) {
            log.error("test reportRisk error,{}", eventDO, e);
        }
        return "failed";
    }

}
