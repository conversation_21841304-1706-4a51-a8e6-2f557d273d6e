package com.kikitrade.kevent.dal.builder.impl;

import com.alicloud.openservices.tablestore.model.ColumnValue;
import com.alicloud.openservices.tablestore.model.Condition;
import com.alicloud.openservices.tablestore.model.RowExistenceExpectation;
import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.Query;
import com.alicloud.openservices.tablestore.model.search.query.TermQuery;
import com.alicloud.openservices.tablestore.model.search.sort.FieldSort;
import com.alicloud.openservices.tablestore.model.search.sort.Sort;
import com.alicloud.openservices.tablestore.model.search.sort.SortOrder;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import com.kikitrade.kevent.dal.builder.UserRiskBuilder;
import com.kikitrade.kevent.dal.model.UserRiskDO;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * User risk builder impl
 *
 * <AUTHOR>
 * @create 2022/6/15 10:16 上午
 * @modify
 */

@Component
@Slf4j
public class UserRiskBuilderImpl extends WideColumnStoreBuilder<UserRiskDO> implements UserRiskBuilder {

    @PostConstruct
    public void init() {
        init(UserRiskDO.class);
    }

    @Override
    public UserRiskDO get(String customerId) {
        UserRiskDO userRiskDO = new UserRiskDO();
        userRiskDO.setCustomerId(customerId);
        return getRow(userRiskDO);
    }

    @Override
    public boolean create(UserRiskDO userRiskDO) {
        long created = System.currentTimeMillis();
        userRiskDO.setCreated(created);
        userRiskDO.setModified(created);
        return putRow(userRiskDO, RowExistenceExpectation.EXPECT_NOT_EXIST);
    }

    @Override
    public boolean updateRiskInfo(UserRiskDO userRiskDO) {
        userRiskDO.setModified(System.currentTimeMillis());
        Condition condition = new Condition();
        condition.setRowExistenceExpectation(RowExistenceExpectation.EXPECT_EXIST);
        return updateRow(userRiskDO, Arrays.asList("level", "score", "tags", "modified"), condition);
    }



}
