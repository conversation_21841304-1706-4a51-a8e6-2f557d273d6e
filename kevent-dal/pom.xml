<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.kikitrade</groupId>
        <artifactId>kevent</artifactId>
        <version>2.0.2-SNAPSHOT</version>
    </parent>

    <artifactId>kevent-dal</artifactId>
    <name>kevent-dal</name>
    <description>Demo project for Spring Boot</description>
    <properties>
        <project.property.path>..</project.property.path>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.kikitrade</groupId>
            <artifactId>kevent-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kikitrade</groupId>
            <artifactId>kiki-ots-spring-boot-starter</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
