package com.kikitrade.kevent.common.constant;

import lombok.Getter;

/**
 * @author: penuel
 * @date: 2022/04/28 16:50
 * @desc: TODO
 */
@Getter
public enum EventMessageEnum {

    SUCCESS("0000", "success", true),

    API_ERROR("1000", "api.error"),

    SYSTEM_ERROR_BY_3RD_DEPS("9000", "system.error.by.3rd.deps"),
    // 9000开始，系统相关错误代码
    SYSTEM_OPERATION_FREQUENT("9000", "system.operation.frequent"),
    SYSTEM_SECURITY_PARAM_REQUIRED("9001", "system.security.param.required"),
    SYSTEM_SECURITY_CHECK_FAIL("9002", "system.security.check.fail"),
    SYSTEM_PARAMETER_INVALID("9003", "system.parameter.invalid"),
    SYSTEM_PARAMETER_REQUIRED("9004", "system.parameter.required"),
    SYSTEM_PARAMETER_FORMAT_INCORRECT("9005", "system.parameter.format.incorrect"),
    SYSTEM_PARAMETER_TYPE_MISMATCH("9006", "system.parameter.type.mismatch"),
    SYSTEM_PARAMETER_NEED_DIGITAL("9007", "system.parameter.digital.required"),
    SYSTEM_DATA_NOT_FOUND("9007", "system.data.not.found"),
    SYSTEM_CACHE_REFRESH_FAIL("9008", "system.cache.refresh.fail"),
    SYSTEM_ERROR("9999", "system.error", false),
    ;

    private String key;
    private String code;
    private boolean success;

    EventMessageEnum(String code, String key, boolean success) {
        this.key = key;
        this.code = code;
        this.success = success;
    }

    EventMessageEnum(String code, String key) {
        this.key = key;
        this.code = code;
        this.success = false;
    }
}
