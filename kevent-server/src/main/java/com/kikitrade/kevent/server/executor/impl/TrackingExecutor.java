package com.kikitrade.kevent.server.executor.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kikitrade.kcustomer.api.model.DeviceInfoDTO;
import com.kikitrade.kevent.common.constant.EventConstants;
import com.kikitrade.kevent.common.constant.EventMessageEnum;
import com.kikitrade.kevent.common.exception.EventException;
import com.kikitrade.kevent.dal.model.EventDO;
import com.kikitrade.kevent.server.autoconfigure.KEventProperties;
import com.kikitrade.kevent.server.constant.SLSAlertConstant;
import com.kikitrade.kevent.server.reference.CustomerServiceReference;
import com.kikitrade.kevent.server.util.TwoTuple;
import com.kikitrade.market.common.base.ExchangeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import jakarta.annotation.Resource;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.kikitrade.kevent.common.constant.EventMessageEnum.SYSTEM_PARAMETER_REQUIRED;

/**
 * @author: penuel
 * @date: 2022/5/5 12:20
 * @desc: TODO
 */
@Slf4j
public abstract class TrackingExecutor extends AbstractExecutor {

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private CustomerServiceReference customerServiceReference;

    @Resource
    private KEventProperties kEventProperties;


    @Autowired(required = false)
    private ExchangeService exchangeService;

    protected String fiatCurrency = "USD";

    @Override
    public EventConstants.DeliveryStatus doExecute(EventDO event) {
        //消息幂等性的处理
        try {
            Map<String, Object> param = null;
            try {
                param = mapping(event);
            } catch (EventException e) {
                log.warn(SLSAlertConstant.AlarmSceneEnum.paramCheckFail.getError() + "param:{},message:{}", this.deliveryChannel().name(), JSON.toJSONString(event), e.getMessage());
                if (EventMessageEnum.SYSTEM_DATA_NOT_FOUND.getCode().equals(e.getCode()) || EventMessageEnum.SYSTEM_PARAMETER_REQUIRED.getCode().equals(e.getCode())) {
                    return EventConstants.DeliveryStatus.FAIL;
                }
                return EventConstants.DeliveryStatus.UNKNOWN;
            }
            return track(param);
        } catch (Exception e) {
            log.error("TrackingExecutor doExecute error,{}", JSON.toJSONString(event), e);
        }
        return EventConstants.DeliveryStatus.FAIL;
    }

    /**
     * 映射事件名称
     *
     * @param event
     */
    public abstract Map<String, Object> mapping(EventDO event) throws EventException;

    /**
     * 上报数据
     *
     * @param params
     * @return
     */
    public abstract EventConstants.DeliveryStatus track(Map<String, Object> params);

    public abstract String endpoint();

    protected <T> T apiCall(HttpMethod httpMethod, Class<T> responseClass, Map<String, Object> params, HttpHeaders headers) throws EventException {

        String responseString = null;
        String fullUrl = endpoint();
        try {
            log.info(SLSAlertConstant.DataMonitorEnum.report.getMonitor() + "apiCall start, fullUrl:{}, params:{}", this.deliveryChannel().name(), fullUrl, params);
            HttpEntity<Map<String, Object>> requestEntity;
            if (null == params || params.size() == 0) {
                requestEntity = new HttpEntity<>(headers);
            } else {
                requestEntity = new HttpEntity<>(params, headers);
            }

            ResponseEntity<String> response = restTemplate.exchange(fullUrl, httpMethod, requestEntity, String.class);
            responseString = response.getBody();

            log.info("apiCall response, fullUrl:{}, params:{}, response:{}", fullUrl, params, responseString);
            if (response.getStatusCode().is2xxSuccessful()) {
                log.info(SLSAlertConstant.DataMonitorEnum.reportSuccess.getMonitor(), this.deliveryChannel().name());
                return (T) EventConstants.DeliveryStatus.SUCCESS;
            }
            if (response.getStatusCode().is4xxClientError()) {
                log.error(SLSAlertConstant.AlarmSceneEnum.reportFail.getError() + "apiCall is4xxClientError, fullUrl:{}, params:{}, response:{}", this.deliveryChannel().name(), fullUrl, params, responseString);
                return (T) EventConstants.DeliveryStatus.UNKNOWN;
            }
            if (response.getStatusCode().is5xxServerError()) {
                log.error(SLSAlertConstant.AlarmSceneEnum.reportFail.getError() + "apiCall is5xxServerError, fullUrl:{}, params:{}, response:{}", this.deliveryChannel().name(), fullUrl, params, responseString);
                return (T) EventConstants.DeliveryStatus.UNKNOWN;
            }
            throw new EventException(EventMessageEnum.API_ERROR);
        } catch (EventException pe) {
            log.error(SLSAlertConstant.AlarmSceneEnum.reportFail.getError() + "TrackingExecutor error - apiCall failed, fullUrl:{}, params:{}, response:{}", this.deliveryChannel().name(), fullUrl, params, responseString, pe);
            throw pe;
        } catch (HttpClientErrorException hee) {
            String message = hee.getMessage();
            if (retryErrorMsg(message)) {
                return (T) EventConstants.DeliveryStatus.FAIL;
            }
            log.warn(SLSAlertConstant.AlarmSceneEnum.reportFail.getError() + "TrackingExecutor error - apiCall failed, fullUrl:{}, params:{}, response:{},message:{},statusCode:{}", this.deliveryChannel().name(), fullUrl, params, responseString, message, hee.getStatusCode());
            return (T) EventConstants.DeliveryStatus.UNKNOWN;
        } catch (Exception e) {
            String message = e.getMessage();
            if (retryErrorMsg(message)) {
                return (T) EventConstants.DeliveryStatus.FAIL;
            }
            log.error(SLSAlertConstant.AlarmSceneEnum.reportFail.getError() + "TrackingExecutor error - apiCall error, fullUrl:{}, params:{}, response:{}", this.deliveryChannel().name(), fullUrl, params, responseString, e);
            throw new EventException(EventMessageEnum.API_ERROR);
        }
    }

    private boolean retryErrorMsg(String message) {
        Map<String, String> errorRetry = kEventProperties.getErrorRetry();
        return errorRetry.entrySet().parallelStream().anyMatch(entry -> message.contains(entry.getValue()));
    }

    protected void paramCheckAndSet(MultiValueMap<String, Object> postParameters, JSONObject eventBody, String filedName, String originalFiledName) {
        if (Objects.nonNull(eventBody) && Objects.nonNull(eventBody.get(originalFiledName))) {
            postParameters.add(filedName, eventBody.get(originalFiledName));
        }
    }

    protected void jsonParamCheckAndSet(JSONObject jsonParam, JSONObject eventBody, String filedName, String originalFiledName) {
        if (Objects.nonNull(eventBody) && Objects.nonNull(eventBody.get(originalFiledName))) {
            jsonParam.put(filedName, eventBody.get(originalFiledName));
        }
    }

    protected void paramCheck(String value) throws EventException {
        if (StringUtils.isBlank(value)) {
            throw new EventException(SYSTEM_PARAMETER_REQUIRED);
        }
    }

    protected DeviceInfoDTO getDeviceInfoByCustomerIdOrDeviceId(String customerId, String deviceId) throws EventException {
        DeviceInfoDTO deviceInfoDTO = null;
        if (StringUtils.isNotBlank(deviceId)) {
            deviceInfoDTO = customerServiceReference.getDeviceInfoByDeviceId(deviceId);
        }
        if (deviceInfoDTO != null) {
            return deviceInfoDTO;
        }
        if (StringUtils.isNotBlank(customerId)) {
            deviceInfoDTO = customerServiceReference.getDeviceInfoByCustomerId(customerId);
        }
        if (Objects.isNull(deviceInfoDTO)) {
            log.warn(SLSAlertConstant.AlarmSceneEnum.paramCheckFail.getError() + ",TrackingExecutor fail - customer DeviceInfo not exists,uid:{},", this.deliveryChannel().name(), customerId);
            throw new EventException(EventMessageEnum.SYSTEM_DATA_NOT_FOUND);
        }
        return deviceInfoDTO;
    }

    protected String calFiatCurrency(String revenue, String currency) throws EventException {
        BigDecimal amount = new BigDecimal(revenue);
        BigDecimal fiatRevenue = exchangeCurrencyByRate(currency, fiatCurrency, amount, 2, RoundingMode.DOWN);
        return fiatRevenue.toPlainString();
    }

    /**
     * 币种金额转换
     *
     * @param fromCurrency
     * @param toCurrency
     * @param fromAmount
     * @param precision
     * @param roundingMode
     * @return
     * @throws EventException
     */
    public BigDecimal exchangeCurrencyByRate(String fromCurrency, String toCurrency, BigDecimal fromAmount, int precision, RoundingMode roundingMode) throws EventException {
        //兑换币种和资产币种一致，则直接返回
        if (fromCurrency.equals(toCurrency)) {
            return fromAmount;
        }
        if (null == exchangeService) {
            return BigDecimal.ZERO;
        }
        return exchangeService.exchangeByPrecision(fromAmount, fromCurrency, toCurrency, precision, roundingMode);
    }

    /**
     * 用户无deviceId时，解析客户端自己生成的格式--"saasId_生成的设备ID"
     *
     * @param deviceId
     * @return
     */
    public String getOriginalDeviceId(String deviceId) {
        String saasIdPrefix = kEventProperties.getSaasId().trim() + "_";
        if (deviceId.startsWith(saasIdPrefix)) {
            int i = deviceId.indexOf('_');
            return deviceId.substring(i + 1);
        }
        return deviceId;
    }


    /**
     * 正则获取@@与@@之间的事件名后，校验事件名合法性
     *
     * @param configKey
     * @param name
     * @return
     */
    protected boolean regularCheck(String configKey, String name) {
        String regx = "__(.*?)__";
        Pattern pattern = Pattern.compile(regx);
        Matcher m = pattern.matcher(configKey);
        while (m.find()) {
            String regKey = m.group(1);
            return name.equals(regKey);
        }
        return false;
    }

    /**
     * 获取外部传入的原始事件名称和处理后的事件后缀
     *
     * @param name
     * @return
     */
    protected TwoTuple<String, String> parseOriginalName(String name) {
        if (name.endsWith(_first)) {
            String originalName = name.substring(0, name.lastIndexOf(_first));
            //首次事件的后缀为first
            return new TwoTuple<>(originalName, "first");
        }
        //默认无后缀
        return new TwoTuple<>(name, "");
    }

}
