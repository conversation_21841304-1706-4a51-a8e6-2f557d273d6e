//package com.kikitrade.kevent.server.service;
//
//import java.util.Map;
//
///**
// * 风控服务
// *
// * <AUTHOR>
// * @create 2022/10/18 10:24
// * @modify
// */
//public interface AliyunRiskService {
//    String CHARSET_UTF8 = "utf8";
//    String ISO8601_DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ss'Z'";
//    String ALGORITHM = "UTF-8";
//    String SEPARATOR = "&";
//    String DECISION_SERVICE = "saf_de";
//
//    int TOKEN_REFRESH_INTERVAL_TIME = 30 * 60 * 1000;
//
//    String configValue(String key);
//
//    /**
//     * 返回阿里云风控的基础配置
//     *
//     * @return
//     */
//    Map<String, String> config();
//
//    String domain();
//
//    SHPLResponse call(SHPLRequest shplRequest);
//
//    class SHPLResponse {
//        private Integer code;
//        private String message;
//        private Map<Object, Object> data;
//        private String requestId;
//
//        public Integer getCode() {
//            return code;
//        }
//
//        public void setCode(Integer code) {
//            this.code = code;
//        }
//
//        public String getMessage() {
//            return message;
//        }
//
//        public void setMessage(String message) {
//            this.message = message;
//        }
//
//        public Map<Object, Object> getData() {
//            return data;
//        }
//
//        public void setData(Map<Object, Object> data) {
//            this.data = data;
//        }
//
//        public String getRequestId() {
//            return requestId;
//        }
//
//        public void setRequestId(String requestId) {
//            this.requestId = requestId;
//        }
//    }
//
//    class SHPLRequest {
//
//        private String service;
//        private String eventCode;
//        private Map<String, Object> serviceParameters;
//
//        public String getService() {
//            return service;
//        }
//
//        public void setService(String service) {
//            this.service = service;
//        }
//
//        public String getEventCode() {
//            return eventCode;
//        }
//
//        public void setEventCode(String eventCode) {
//            this.eventCode = eventCode;
//        }
//
//        public Map<String, Object> getServiceParameters() {
//            return serviceParameters;
//        }
//
//        public void setServiceParameters(Map<String, Object> serviceParameters) {
//            this.serviceParameters = serviceParameters;
//        }
//
//    }
//
//    class SHPLHttpClientConfig {
//
//        private boolean ignoreSSLCerts = false;
//
//        private int connectionRequestTimeout = 5000;
//        private int connectTimeout = 5000;
//        private int socketTimeout = 5000;
//
//        private int maxRequests = 400;
//        private int maxRequestsPerHost = 200;
//
//        public boolean isIgnoreSSLCerts() {
//            return ignoreSSLCerts;
//        }
//
//        public void setIgnoreSSLCerts(boolean ignoreSSLCerts) {
//            this.ignoreSSLCerts = ignoreSSLCerts;
//        }
//
//        public int getConnectionRequestTimeout() {
//            return connectionRequestTimeout;
//        }
//
//        public void setConnectionRequestTimeout(int connectionRequestTimeout) {
//            this.connectionRequestTimeout = connectionRequestTimeout;
//        }
//
//        public int getConnectTimeout() {
//            return connectTimeout;
//        }
//
//        public void setConnectTimeout(int connectTimeout) {
//            this.connectTimeout = connectTimeout;
//        }
//
//        public int getSocketTimeout() {
//            return socketTimeout;
//        }
//
//        public void setSocketTimeout(int socketTimeout) {
//            this.socketTimeout = socketTimeout;
//        }
//
//        public int getMaxRequests() {
//            return maxRequests;
//        }
//
//        public void setMaxRequests(int maxRequests) {
//            this.maxRequests = maxRequests;
//        }
//
//        public int getMaxRequestsPerHost() {
//            return maxRequestsPerHost;
//        }
//
//        public void setMaxRequestsPerHost(int maxRequestsPerHost) {
//            this.maxRequestsPerHost = maxRequestsPerHost;
//        }
//    }
//
//    class ShplToken {
//        private String shplToken;
//        private String secretKey;
//        private String errMsg;
//        private String requestId;
//
//        public String getShplToken() {
//            return shplToken;
//        }
//
//        public void setShplToken(String shplToken) {
//            this.shplToken = shplToken;
//        }
//
//        public String getSecretKey() {
//            return secretKey;
//        }
//
//        public void setSecretKey(String secretKey) {
//            this.secretKey = secretKey;
//        }
//
//        public String getErrMsg() {
//            return errMsg;
//        }
//
//        public void setErrMsg(String errMsg) {
//            this.errMsg = errMsg;
//        }
//
//        public String getRequestId() {
//            return requestId;
//        }
//
//        public void setRequestId(String requestId) {
//            this.requestId = requestId;
//        }
//    }
//}
