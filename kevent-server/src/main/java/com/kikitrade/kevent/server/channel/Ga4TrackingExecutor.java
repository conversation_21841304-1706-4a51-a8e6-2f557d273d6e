package com.kikitrade.kevent.server.channel;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.kikitrade.kcustomer.api.model.DeviceInfoDTO;
import com.kikitrade.kevent.common.constant.EventConstants;
import com.kikitrade.kevent.common.exception.EventException;
import com.kikitrade.kevent.dal.model.EventDO;
import com.kikitrade.kevent.server.autoconfigure.KEventProperties;
import com.kikitrade.kevent.server.executor.impl.TrackingExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import static com.kikitrade.kevent.common.constant.EventMessageEnum.SYSTEM_PARAMETER_INVALID;

/**
 * <AUTHOR>
 */
@Slf4j
@Component("ga4TrackingExecutor")
@ConditionalOnProperty(name = {"kevent.google-analytics.api_url"})
public class Ga4TrackingExecutor extends TrackingExecutor {

    @Resource
    KEventProperties kEventProperties;

    String androidDeviceType = "android";
    String iosDeviceType = "ios";

    @Override
    public EventConstants.DeliveryChannel deliveryChannel() { // 投递渠道
        return EventConstants.DeliveryChannel.GOOGLE_ANALYTICS;
    }

    Map<String, String> getGoogleAnalyticsConfig() {
        return kEventProperties.getGoogleAnalytics();
    }

    /**
     * apiUrl cache, HashMap<deviceType, apiUrl>
     */
    private final HashMap<String, String> apiUrlMap = Maps.newHashMap();

    /**
     * currentToken will be refreshed everytime
     */
    private volatile String currentApiUrl = "";

    @Override
    public String endpoint() {
        return currentApiUrl;
    }

    /**
     * fill apiUrlMap
     * refresh apiUrl
     * @param source platform android ios and web
     */
    private void refreshCurrentApiUrl(String source) throws EventException {
        if(apiUrlMap.isEmpty()){
            if(StringUtils.isBlank(getGoogleAnalyticsConfig().get(EventConstants.GoogleAnalyticsConfig.apiUrl))){
                log.error("google analytics config api_url is empty, please check config {kevent.google-analytics.api_url}");
                throw new EventException(SYSTEM_PARAMETER_INVALID);
            }
            if(kEventProperties.getGoogleAnalyticsToken().isEmpty()){
                log.error("google analytics token config is empty, please check config {kevent.google-analytics-token}");
                throw new EventException(SYSTEM_PARAMETER_INVALID);
            }
            // if empty, fill cache
            kEventProperties.getGoogleAnalyticsToken().forEach((k, v) -> {
                if(StringUtils.isNotBlank(v)){
                    String deviceTypeKey = Arrays.asList(androidDeviceType, iosDeviceType).contains(k) ?
                            EventConstants.GoogleAnalyticsConstant.firebaseAppId :
                            EventConstants.GoogleAnalyticsConstant.measurementId;
                    String value = getGoogleAnalyticsConfig().get(EventConstants.GoogleAnalyticsConfig.apiUrl) + "?"
                            + EventConstants.GoogleAnalyticsConstant.apiSecret + "=" + v.substring(0, v.indexOf("|")) + "&"
                            + deviceTypeKey + "=" + v.substring(v.indexOf("|") + 1);
                    apiUrlMap.put(k, value);
                }
            });
        }
        if( !apiUrlMap.containsKey(source)){
            log.error("current deviceType = {}, apiUrl config or token is null, please check config", source);
            throw new EventException(SYSTEM_PARAMETER_INVALID);
        }
        this.currentApiUrl = apiUrlMap.get(source);
        log.info("current deviceType = {}, currentApiUrl = {}", source, this.currentApiUrl);
    }

    @Override
    public boolean isContinue(EventDO input) {
        boolean result = super.isContinue(input);
        if (result) {
            Set<String> keySet = kEventProperties.getGoogleAnalyticsEventName().keySet();
            return keySet.parallelStream().anyMatch(k -> regularCheck(k, parseOriginalName(input.getName()).getFirst()));
        }
        return false;
    }

    @Override
    public Map<String, Object> mapping(EventDO event) throws EventException {
        log.info("mapping event {}", event);
        Map<String, Object> postParameters = new HashMap<>(16);
        // 优先使用 deviceId 获取设备信息，但是 deviceId 可能为空，为空的情况下使用 customerId 获取最新的 DeviceInfo，
        DeviceInfoDTO deviceInfo = getDeviceInfoByCustomerIdOrDeviceId(event.getCustomerId(),event.getDeviceId());
        // check deviceType
        log.info("mapping deviceInfo {}", deviceInfo);
        if(StringUtils.isBlank(deviceInfo.getDeviceType()) || !(Arrays.asList(EventConstants.GoogleAnalyticsConstant.iosDeviceType,
                EventConstants.GoogleAnalyticsConstant.androidDeviceType, EventConstants.GoogleAnalyticsConstant.webDeviceType,
                EventConstants.GoogleAnalyticsConstant.tabletDeviceType).contains(deviceInfo.getDeviceType()))){
            log.warn("current device_type is blank or not ga stream channel, event.id = {}", event.getId());
            return Maps.newHashMap();
        }

        // refresh currentApiUrl
        refreshCurrentApiUrl(deviceInfo.getDeviceType());

        // put user client info
        JSONObject body = JSONObject.parseObject(JSONObject.parseObject(event.getBody()).getString("body"));
        String clientId = body.getString(EventConstants.GoogleAnalyticsConstant.clientId);
        if(StringUtils.isBlank(clientId) || StringUtils.isBlank(event.getCustomerId())){
            log.error("event customerId or body.clientId is null , event.id = {}, event.body.clientId = {}", event.getId(), clientId);
            return Maps.newHashMap();
        }
        postParameters.put(EventConstants.GoogleAnalyticsConstant.client_id, clientId);
        postParameters.put(EventConstants.GoogleAnalyticsConstant.userId, event.getCustomerId());
        postParameters.put(EventConstants.GoogleAnalyticsConstant.timestampMicros, event.getTime() * 1000);

        // put userProperties
        postParameters.put(EventConstants.GoogleAnalyticsConstant.userProperties, new JSONObject()
                .fluentPut(EventConstants.GoogleAnalyticsConstant.customerTier, new JSONObject()
                        .fluentPut(EventConstants.GoogleAnalyticsConstant.customerTireValue, new JSONObject()
                                .fluentPut(EventConstants.GoogleAnalyticsConstant.customerId, event.getCustomerId())
                                .fluentPut(EventConstants.GoogleAnalyticsConstant.operation, body.getString("description"))
                                .toJSONString())));

        // 上报 ga4 的事件内容自定义的部分，只能在 event.params 中添加
        if (iosDeviceType.equals(deviceInfo.getDeviceType()) && StringUtils.isNotBlank(deviceInfo.getDeviceId())) {
            body.put(EventConstants.GoogleAnalyticsConstant.idfa, getOriginalDeviceId(deviceInfo.getDeviceId()));
        }
        if (androidDeviceType.equals(deviceInfo.getDeviceType()) && StringUtils.isNotBlank(deviceInfo.getDeviceId())) {
            body.put(EventConstants.GoogleAnalyticsConstant.gid, getOriginalDeviceId(deviceInfo.getDeviceId()));
        }

        // put gaEvents
        postParameters.put(EventConstants.GoogleAnalyticsConstant.events, new JSONArray()
                .fluentAdd(new JSONObject()
                        .fluentPut(EventConstants.GoogleAnalyticsConstant.name, event.getName())
                        .fluentPut(EventConstants.GoogleAnalyticsConstant.params, body)));

        log.info("ga4TrackingExecutor mapping postParameters = {}", JSONObject.toJSONString(postParameters));
        return postParameters;
    }

    @Override
    public EventConstants.DeliveryStatus track(Map<String, Object> params) {
        if(params == null || params.size() == 0){
            log.warn("GoogleAnalytics create request param fail");
            return EventConstants.DeliveryStatus.UNKNOWN;
        }
        try {
            HttpHeaders headers = new HttpHeaders();
            return apiCall(HttpMethod.POST, EventConstants.DeliveryStatus.class, params, headers);
        } catch (Exception e) {
            log.warn("GoogleAnalytics track data error", e);
            return EventConstants.DeliveryStatus.FAIL;
        }
    }

}
