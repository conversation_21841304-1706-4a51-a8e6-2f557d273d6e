package com.kikitrade.kevent.server.executor;

import com.alibaba.fastjson.JSONObject;
import com.kikitrade.kevent.dal.model.EventDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * @author: penuel
 * @date: 2022/5/5 12:17
 * @desc:
 */
@Service
@Slf4j
public class ExecutorManager{
        /**
         * 1 第一次分发
         * 2、10 分钟后检查（没有重试的的）
         * 3、如果异常 10 分钟之后又重试
         * @param input
         * @return
         */
        public boolean process(EventDO input) {
            Boolean result = true;
            if (CollectionUtils.isEmpty(executors)) {
                return result;
            }
            for (Executor executor : executors) {
                log.info("ExecutorManager process input{}, executor:{}", JSONObject.toJSONString(input), executor.getClass());
                if (executor.isContinue(input)) {
                    //异步执行
                    executor.execute(input);
                    result = result && false;
                    continue;
                }
            }
            return result;
        }
        @Autowired(required = false)
        private List<Executor> executors;
}
