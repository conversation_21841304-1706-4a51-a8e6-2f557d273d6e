package com.kikitrade.kevent.server.service;

import com.kikitrade.kevent.dal.model.UserRiskDO;

/**
 * User risk service interface
 *
 * <AUTHOR>
 * @create 2022/6/15 6:41 下午
 * @modify
 */
public interface UserRiskService {

    /**
     * Get single user fraud risk info
     *
     * @param customerId
     * @return
     */
    UserRiskDO get(String customerId);

    /**
     * update basic info for single user risk
     *
     * @param userRiskDO
     * @return
     */
    boolean updateRisk(UserRiskDO userRiskDO);
}
