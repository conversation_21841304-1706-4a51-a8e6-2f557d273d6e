package com.kikitrade.kevent.server.autoconfigure;

import com.kikitrade.framework.common.thread.ExecutorsFactory;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

import java.util.concurrent.ExecutorService;

/**
 * @author: penuel
 * @date: 2021/9/7 11:15
 * @desc: TODO
 */
@Configuration
@EnableConfigurationProperties(KEventProperties.class)
public class KEventAutoConfiguration {

    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }

    @Bean(name = "keventExecutor")
    public ExecutorService ttlExecutorService() {
        return ExecutorsFactory.ttlExecutorService("keventExecutor");
    }

}
