package com.kikitrade.kevent.server.channel;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kikitrade.kcustomer.api.model.block.CreateBlockRequest;
import com.kikitrade.kcustomer.api.model.block.CreateBlockResponse;
import com.kikitrade.kcustomer.api.model.block.TimePeriodEnum;
import com.kikitrade.kcustomer.api.service.RemoteBlockBusinessService;
import com.kikitrade.kcustomer.common.constants.BlockConstants;
import com.kikitrade.kevent.common.constant.EventConstants;
import com.kikitrade.kevent.common.constant.EventMessageEnum;
import com.kikitrade.kevent.common.constant.RiskConstants;
import com.kikitrade.kevent.common.constant.UserFraudRiskLevel;
import com.kikitrade.kevent.common.exception.EventException;
import com.kikitrade.kevent.common.model.RiskExtendReply;
import com.kikitrade.kevent.common.util.DateUtil;
import com.kikitrade.kevent.common.util.SHPLClient;
import com.kikitrade.kevent.dal.builder.UserStatisticsBuilder;
import com.kikitrade.kevent.dal.model.EventDO;
import com.kikitrade.kevent.dal.model.StatField;
import com.kikitrade.kevent.dal.model.UserRiskDO;
import com.kikitrade.kevent.dal.model.UserStatisticsDO;
import com.kikitrade.kevent.server.autoconfigure.KEventProperties;
import com.kikitrade.kevent.server.constant.SLSAlertConstant;
import com.kikitrade.kevent.server.executor.impl.TrackingExecutor;
import com.kikitrade.kevent.server.service.TemplateService;
import com.kikitrade.kevent.server.service.TemplateTask;
import com.kikitrade.kevent.server.service.UserRiskService;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.assertj.core.util.Lists;
import org.assertj.core.util.Strings;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;

import static com.kikitrade.kevent.common.constant.EventConstants.*;

@Slf4j
@Component
@ConditionalOnProperty(name = {"kevent.aliyun-risk.domain"})
public class AliyunRiskTrackingExecutor extends TrackingExecutor {

    public static final String FIAT_SUFFIX = "Usd";
    public static final String SPLIT = "\\|";
    public static final String PENDING = "PENDING";
    public static final String REJECT = "REJECT";
    public static final String BLOCK = "BLOCK";

    @DubboReference(timeout = 10000)
    private RemoteBlockBusinessService remoteBlockBusinessService;

    @Autowired
    private KEventProperties kEventProperties;

    @Autowired
    private TemplateService templateService;

    @Autowired
    private UserStatisticsBuilder userStatisticsBuilder;

    @Autowired
    private UserRiskService userRiskService;

    @Override
    public DeliveryChannel deliveryChannel() {
        return DeliveryChannel.ALIYUN_RISK;
    }

    SHPLClient shplClient;

    @PostConstruct
    public void init() throws Exception {
        String domain = kEventProperties.getAliyunRisk().get(AliyunRiskConfig.domain);
        String region = kEventProperties.getAliyunRisk().get(AliyunRiskConfig.region);
        String ak = kEventProperties.getAliyunRisk().get(AliyunRiskConfig.ak);
        String sk = kEventProperties.getAliyunRisk().get(AliyunRiskConfig.sk);
        this.shplClient = SHPLClient.getInstance(domain, region, ak, sk);
    }

    /**
     * 离线数据上报
     *
     * @param customerId
     */
    public void reportUserStatistics(String customerId) {
        Map<String, Object> parameters = new HashMap<>();
        String eventCode = eventCode(EventName.USER_STATISTICS.getName());
        if (StringUtils.isEmpty(eventCode)) {
            log.error(SLSAlertConstant.AlarmSceneEnum.paramCheckFail.getError() + "param:{},", this.deliveryChannel().name(), EventName.USER_STATISTICS.getName());
            return;
        }
        parameters.put(RiskConstants.RiskReportParam.EVENT_CODE.key(), eventCode);
        parameters.put(RiskConstants.RiskReportParam.CUSTOMER_ID.key(), customerId);
        parameters.put(RiskConstants.RiskReportParam.TIME.key(), System.currentTimeMillis());
        parameters.put(RiskConstants.RiskReportParam.TEMPLATE_NAME.key(), EventName.USER_STATISTICS.getTemplateName().name());
        parameters.put(RiskConstants.RiskReportParam.ENV.key(), kEventProperties.getEnv());
        // fill user statistics
        fillUserStatistics(parameters, customerId);
        DeliveryStatus track = track(parameters);
        log.info("reportUserStatistics customerId {}, track {}", customerId, track);
    }

    @Override
    public Map<String, Object> mapping(EventDO event) throws EventException {
        try {
            // 登录事件需要实时上报离线事件
            EventName eventName = EventName.valueOf(event.getName().toUpperCase(Locale.ROOT));
            if (eventName == EventName.LOGIN) {
                reportUserStatistics(event.getCustomerId());
            }
        } catch (Exception e) {
            log.error("login reportUserStatistics error, {}", event, e);
        }
        // parameters
        Map<String, Object> parameters = new HashMap<>();
        // fill business parameters
        fillEventBody(parameters, event);
        // fill user statistics
        fillUserStatistics(parameters, event.getCustomerId());

        return parameters;
    }

    @Override
    public boolean isContinue(EventDO input) {
        boolean result = super.isContinue(input);
        if (result) {
            Set<String> eventNames = kEventProperties.getAliyunRiskEventName().keySet();
            if (eventNames.stream().anyMatch(k -> regularCheck(k, input.getName()))) {
                return true;
            }
            return false;
        }
        return false;
    }

    /**
     * 填充实时事件信息
     *
     * @param parameters
     * @param event
     */
    protected void fillEventBody(Map<String, Object> parameters, EventDO event) throws EventException {
        log.info("fillEventBody start, parameters:{}, event:{}", parameters, event);
        EventName eventName = EventName.valueOf(event.getName().toUpperCase(Locale.ROOT));
        RiskConstants.Template templateName = eventName.getTemplateName();
        if (templateName == null) {
            log.error(SLSAlertConstant.AlarmSceneEnum.paramCheckFail.getError() + "param:{},", this.deliveryChannel().name(), eventName);
            throw new EventException(EventMessageEnum.SYSTEM_PARAMETER_REQUIRED);
        }
        parameters.put(RiskConstants.RiskReportParam.EVENT_CODE.key(), eventCode(event.getName()));
        parameters.put(RiskConstants.RiskReportParam.CUSTOMER_ID.key(), event.getCustomerId());
        parameters.put(RiskConstants.RiskReportParam.TIME.key(), event.getTime());
        parameters.put(RiskConstants.RiskReportParam.UID.key(), event.getUid());
        parameters.put(RiskConstants.RiskReportParam.DEVICE_TOKEN.key(), event.getDeviceToken());
        parameters.put(RiskConstants.RiskReportParam.IP.key(), event.getIp());
        parameters.put(RiskConstants.RiskReportParam.DEVICE_ID.key(), event.getDeviceId());
        parameters.put(RiskConstants.RiskReportParam.ENV.key(), kEventProperties.getEnv());


        /**
         * plain body
         */
        JSONObject eventBody = JSONObject.parseObject(event.getBody());
        if (eventBody != null) {
            Set<String> keys = eventBody.keySet();
            for (String key : keys) {
                switch (key) {
                    // for decimal case
                    case EventBodyConstant.revenue:
                        parameters.put(key, eventBody.getBigDecimal(key).toPlainString());
                        break;
                    default:
                        parameters.put(key, eventBody.get(key));
                        break;
                }
            }
        }

        /**
         * nested body
         */
        JSONObject body = eventBody.getJSONObject(EventBodyConstant.body);
        if (body != null) {
            Set<String> bodyKeys = body.keySet();
            for (String key : bodyKeys) {
                parameters.put(key, body.get(key));
            }
        }
        parameters.put(RiskConstants.RiskReportParam.TEMPLATE_NAME.key(), templateName.name());

        RiskConstants.RiskEvent riskEvent = RiskConstants.RiskEvent.fromEventName(event.getName());
        /**
         * fill price
         */
        String currency = eventBody.getString(EventBodyConstant.currency);
        if (StringUtils.isNotBlank(currency)) {
            BigDecimal price;
            if (riskEvent != null && riskEvent.getChannel() != RiskConstants.RiskChannel.C2C && parameters.containsKey(RiskConstants.RiskReportParam.PRICE.key())) {
                price = body.getBigDecimal(RiskConstants.RiskReportParam.PRICE.key());
            } else {
                price = exchangeCurrencyByRate(currency, fiatCurrency, BigDecimal.ONE, 2, RoundingMode.DOWN);
            }
            parameters.put(RiskConstants.RiskReportParam.PRICE.key(), price.toPlainString());
            parameters.put(RiskConstants.RiskReportParam.VALUE.key(), eventBody.getBigDecimal(EventBodyConstant.revenue).multiply(price).toPlainString());
        }
        if (body != null) {
            fillExpandUsd(parameters, body, "filledVolume", "quoteCurrency");
        }
        /**
         * fill risk event config
         */
        if (riskEvent != null) {
            parameters.put(RiskConstants.RiskReportParam.CHANNEL.key(), riskEvent.getChannel().key());
            parameters.put(RiskConstants.RiskReportParam.STATUS.key(), riskEvent.getStatus().key());
            parameters.put(RiskConstants.RiskReportParam.TYPE.key(), riskEvent.getType().key());
        }
        log.info("fillEventBody end, parameters:{}, event:{}", parameters, event);
//        /**
//         * for pay domain
//         */
//        RiskConstants.PayEvent payEvent = RiskConstants.PayEvent.fromEventName(event.getName());
//        if (payEvent != null) {
//            parameters.put(RiskConstants.RiskReportParam.CHANNEL.key(), payEvent.getChannel().key());
//            parameters.put(RiskConstants.RiskReportParam.STATUS.key(), payEvent.getStatus().key());
//            parameters.put(RiskConstants.RiskReportParam.TYPE.key(), payEvent.getType().key());
//            parameters.put(RiskConstants.RiskReportParam.TEMPLATE_NAME.key(), RiskConstants.Template.Pay.name());
//            BigDecimal price;
//            // price not filled case for withdraw/deposit
//            // c2c price means the fiat_currency/crypto_currency
//            String currency = eventBody.getString(EventBodyConstant.currency);
//            if (fiatCurrency.equalsIgnoreCase(currency)) {
//                price = BigDecimal.ONE;
//            } else if (payEvent.getChannel() != RiskConstants.PayChannel.C2C
//                    && parameters.containsKey(RiskConstants.RiskReportParam.PRICE.key())) {
//                price = body.getBigDecimal(RiskConstants.RiskReportParam.PRICE.key());
//            } else {
//                price = exchangeCurrencyByRate(currency, fiatCurrency, BigDecimal.ONE, 2, RoundingMode.DOWN);
//                parameters.put(RiskConstants.RiskReportParam.PRICE.key(), price.toPlainString());
//            }
//            parameters.put(RiskConstants.RiskReportParam.VALUE.key(), eventBody.getBigDecimal(EventBodyConstant.revenue).multiply(price).toPlainString());
//        } else {
//            parameters.put(RiskConstants.RiskReportParam.TEMPLATE_NAME.key(), RiskConstants.Template.Trade.name());
//            fillExpandUsd(parameters, body, "filledVolume", "quoteCurrency");
//        }

    }

    /**
     * 填充字段的USD 扩展字段
     *
     * @param parameters
     * @param body
     * @param amountField
     * @param currencyField
     * @throws EventException
     */
    protected void fillExpandUsd(Map<String, Object> parameters, JSONObject body,
                                 String amountField, String currencyField) throws EventException {
        String amount = body.getString(amountField);
        String quoteCurrency = body.getString(currencyField);
        if (StringUtils.isEmpty(amount) && StringUtils.isEmpty(quoteCurrency)) {
            return;
        }
        parameters.put(amountField + FIAT_SUFFIX, calFiatCurrency(amount, quoteCurrency));
    }

    protected String eventCode(String eventName) {
        return kEventProperties.getAliyunRiskEventName().get("__" + eventName.toLowerCase() + "__");
    }

    protected String oldTemplateKey(String templateKey) {
        return kEventProperties.getEventTemplate().get(templateKey);
    }

    /**
     * 填充离线统计数据
     *
     * @param parameters
     * @param customerId
     */
    protected void fillUserStatistics(Map<String, Object> parameters, String customerId) {
        log.info("fillUserStatistics start, parameters:{}, customerId:{}", parameters, customerId);
        String statDate = new SimpleDateFormat("yyyy-MM-dd").format(Calendar.getInstance(Locale.CHINA).getTime());
        UserStatisticsDO userStatisticsDO = userStatisticsBuilder.getLatest(customerId);
        if (userStatisticsDO == null) {
            userStatisticsDO = new UserStatisticsDO();
            userStatisticsDO.setCustomerId(customerId);
            userStatisticsDO.setAcStaticDate(statDate);
        }

        Class<? extends UserStatisticsDO> clazz = userStatisticsDO.getClass();
        Field[] fields = clazz.getDeclaredFields();

        for (Field field : fields) {
            field.setAccessible(true);
            StatField annotation = field.getAnnotation(StatField.class);
            String key = annotation.name();
            // value
            Object value = null;
            try {
                value = field.get(userStatisticsDO);
            } catch (IllegalAccessException e) {
                log.error("access field error, key={} obj={}", key, JSON.toJSONString(userStatisticsDO), e);
            }
            // process
            if (!annotation.include()) {
                continue;
            }
            if (value == null) {
                StatField.Type type = annotation.type();
                switch (type) {
                    case STRING:
                        value = "";
                        break;
                    case DECIMAL:
                        value = BigDecimal.ZERO;
                        break;
                    case INTEGER:
                        value = Integer.valueOf(0);
                        break;
                }
            }
            parameters.put(key, value);
        }
        log.info("fillUserStatistics end, parameters:{}, userStatisticsDO:{}", parameters, userStatisticsDO);
    }

    protected SHPLClient.SHPLResponse report(String eventCode, String body) throws Exception {
        if (shplClient == null) {
            throw new Exception("report error since shplClient is null, eventCode " + eventCode);
        }
        return shplClient.getSHPLResponse(eventCode, body);
    }

    @Override
    public DeliveryStatus track(Map<String, Object> params) {
        log.info("aliyun track params = {}", JSON.toJSONString(params));
        String eventCode = (String) params.get(RiskConstants.RiskReportParam.EVENT_CODE.key());
        String newTemplateKey = params.get(RiskConstants.RiskReportParam.TEMPLATE_NAME.key()).toString();
        // eventCode=de_ayfjin4069(新事件)|de_ayfjin4069(老事件)
        String[] split = eventCode.split(SPLIT);
        DeliveryStatus deliveryStatus = DeliveryStatus.SUCCESS;
        if (StringUtils.isNotBlank(split[0])) {
            deliveryStatus = reportRisk(params, split[0], newTemplateKey);
        }

        /**
         * TODO 兼容老事件  后续删除
         */
        DeliveryStatus oldDeliveryStatus = DeliveryStatus.SUCCESS;
        String oldTemplateKey = oldTemplateKey(newTemplateKey);
        if (!Strings.isNullOrEmpty(oldTemplateKey) && split.length > 1) {
            oldDeliveryStatus = reportRisk(params, split[1], oldTemplateKey);
        }
        if (deliveryStatus == DeliveryStatus.FAIL || oldDeliveryStatus == DeliveryStatus.FAIL) {
            return DeliveryStatus.FAIL;
        }
        return DeliveryStatus.SUCCESS;
    }

    private DeliveryStatus reportRisk(Map<String, Object> params, String eventCode, String templateKey) {
        String customerId = (String) params.get(RiskConstants.RiskReportParam.CUSTOMER_ID.key());
        TemplateTask task = new TemplateTask();
        RiskConstants.Template template = RiskConstants.Template.valueOf(templateKey);
        task.setTemplateName(template.templateName());
        // replace parameters into tpl
        Map<String, Object> parameterMap = params;
        parameterMap.put(RiskConstants.RiskReportParam.EVENT_CODE.key(), eventCode);
        task.setParameterMap(parameterMap);
        String body = templateService.render(task);
        log.info("RiskEngine request body={} params={}", body, JSON.toJSONString(parameterMap));

        try {
            SHPLClient.SHPLResponse res = report(eventCode, body);

            log.info("RiskEngine result {} ****** {}", customerId, JSON.toJSONString(res));

            int code = res.getCode();
            if (200 != code) {
                log.error("RiskEngine request not success, body={}, res={}", body, res.getData());
                return DeliveryStatus.FAIL;
            }

            boolean updateBlockBusiness = updateBlockBusiness(res.getRequestId(), customerId, res.getData(), eventCode, template);
            boolean updateRiskByJsonData = updateRiskByJsonData(customerId, res.getData());
            log.info("RiskEngine body {}, updateBlockBusiness {}, updateRiskByJsonData {}", body, updateBlockBusiness, updateRiskByJsonData);
            if (updateBlockBusiness && updateRiskByJsonData) {
                return DeliveryStatus.SUCCESS;
            }

        } catch (Exception e) {
            log.error("RiskEngine call error, body={}", body, e);
        }

        return DeliveryStatus.FAIL;
    }

    @Nullable
    private boolean updateRiskByJsonData(String customerId, Map<Object, Object> data) {
        log.info("updateRiskByJsonData customerId: {}, data:{}", customerId, JSON.toJSONString(data));
        // JSONObject data = obj.getJSONObject(RiskConstants.RiskReplyParam.DATA.key());
        String tags = (String) data.get(RiskConstants.RiskReplyParam.DATA_TAGS.key());
        BigDecimal score = (BigDecimal) data.get(RiskConstants.RiskReplyParam.DATA_SCORE.key());
        score = null == score ? BigDecimal.ZERO : score;

        if (BigDecimal.ZERO.compareTo(score) == 0 && StringUtils.isBlank(tags)) {
            // skip if tags is empty and score is also empty
            // return true;
            log.info("RiskEngine updateRiskByJsonData no risk user={} obj={}", customerId, JSON.toJSONString(data));
            return true;
        }

        List<String> finalTags = new ArrayList<>();
        if (null != tags) {
            finalTags = Lists.newArrayList(tags.split(","));
            finalTags.remove("none"); //去除掉默认的tag
        }
        double finalScore = score.doubleValue();

        // 适配新风控后，aliyun不做去重处理，需要自行处理
        String[] uids = customerId.split(",");
        UserRiskDO userRiskDO = new UserRiskDO();
        userRiskDO.setCustomerId(uids[0]);
        userRiskDO.setTags(String.join(",", finalTags));
        userRiskDO.setScore(finalScore);
        userRiskDO.setLevel(UserFraudRiskLevel.fromScore(finalScore).name().toLowerCase());
        userRiskDO.setSaasId(kEventProperties.getSaasId());

        return userRiskService.updateRisk(userRiskDO);
    }

    @Nullable
    private boolean updateBlockBusiness(String requestId, String customerId, Map<Object, Object> data, String eventCode, RiskConstants.Template template) {
        String tags = (String) data.get(RiskConstants.RiskReplyParam.DATA_TAGS.key());
        String finalDecision = (String) data.get(RiskConstants.RiskReplyParam.DATA_FINAL_DECISION.key());
        String extend = (String) data.get(RiskConstants.RiskReplyParam.EXTEND.key());
        BigDecimal score = (BigDecimal) data.get(RiskConstants.RiskReplyParam.DATA_SCORE.key());
        RiskExtendReply riskExtendReply = JSONObject.parseObject(extend, RiskExtendReply.class);
        log.info("[new risk result]customerId={}, template={}, eventCode={}, riskExtendReply={}, tags={}, finalDecision={}, score={}, requestId={}", customerId, template, eventCode, riskExtendReply, tags, finalDecision, score, requestId);
        BlockConstants.FinalDecision decision = null;
        // 老事件风控结果||新事件风控结果
        if (PENDING.equals(finalDecision) || (riskExtendReply != null && riskExtendReply.getResult() != null && riskExtendReply.getResult().contains(PENDING))) {
            decision = BlockConstants.FinalDecision.PENDING;
        }
        if (REJECT.equals(finalDecision) || (riskExtendReply != null && riskExtendReply.getResult() != null && (riskExtendReply.getResult().contains(REJECT) || riskExtendReply.getResult().contains(BLOCK)))) {
            decision = BlockConstants.FinalDecision.BLOCK;
        }
        // 无风险
        if (decision == null) {
            log.info("updateBlockBusiness ignore no risk, customerId {}, finalDecision {}", customerId, finalDecision);
            return true;
        }
        // 兼容老事件
        if (riskExtendReply.getTarget() == null) {
            log.info("updateBlockBusiness ignore old event, customerId {}, finalDecision {}", customerId, finalDecision);
            return true;
        }
        // aliyun会将所有策略结果合并，按优先级返回 eg: {"extend":"{\"period\":\"30,30\",\"business\":\"AAA,AAA\"}"}
        String[] allEnv = null;
        if (StringUtils.isNotBlank(riskExtendReply.getEnv())) {
            allEnv = riskExtendReply.getEnv().split(",");
            if (!Arrays.stream(allEnv).toList().contains(kEventProperties.getEnv())) {
                return true;
            }
        }
        String[] allTarget = riskExtendReply.getTarget().split(",");
        String[] allTargetType = riskExtendReply.getTargetType().split(",");
        String[] allResult = riskExtendReply.getResult().split(",");
        String[] allBusiness = riskExtendReply.getBusiness().split(",");
        String[] allPeriod = riskExtendReply.getPeriod().split(",");
        String[] allNextStep = riskExtendReply.getNextStep().split(",");
        String[] allDeviceToken = null;
        if (StringUtils.isNotBlank(riskExtendReply.getDeviceToken())) {
            allDeviceToken = riskExtendReply.getDeviceToken().split(",");
        }
        List<CreateBlockRequest> requestList = new ArrayList<>();
        for (int i = 0; i < allBusiness.length; i++) {
            List<CreateBlockRequest> request = buildBlockBusiness(requestId, allTarget[i], allTargetType[i], allResult[i], allBusiness[i], allPeriod[i], allNextStep[i], allDeviceToken == null || allDeviceToken.length == 0 ? "" : allDeviceToken[i]);
            if (request != null) {
                requestList.addAll(request);
            }
        }
        List<Boolean> result = new CopyOnWriteArrayList<>();
        List<CompletableFuture<Boolean>> futureList = new CopyOnWriteArrayList<>();
        requestList.parallelStream().forEach(request -> {
            CompletableFuture<Boolean> future = CompletableFuture.supplyAsync(() -> {
                log.info("createBlockBusiness request {}", request);
                CreateBlockResponse blockBusiness = remoteBlockBusinessService.decide(request);
                return blockBusiness == null ? false : blockBusiness.isSuccess();
            }).whenComplete((v, e) -> {
                if (null != e) {
                    log.error("createBlockBusiness failed, request:{}, e:{}", request, e);
                }
                result.add(v);
            });
            futureList.add(future);
        });
        CompletableFuture.allOf(futureList.toArray(CompletableFuture[]::new)).join();
        if (result.contains(false)) {
            return false;
        }
        return true;
    }

    private List<CreateBlockRequest> buildBlockBusiness(String requestId, String target, String targetType, String result, String business, String period, String nextStep, String deviceToken) {
        log.info("buildBlockBusiness target={}, targetType={}, result={}, business={}, period={}, nextStep={}", target, targetType, result, business, period, nextStep);
        BlockConstants.FinalDecision decision = null;
        if (PENDING.equals(result)) {
            decision = BlockConstants.FinalDecision.PENDING;
        }
        if (REJECT.equals(result) || BLOCK.equals(result)) {
            decision = BlockConstants.FinalDecision.BLOCK;
        }
        // 无风险
        if (decision == null) {
            return null;
        }
        List<CreateBlockRequest> createBlockRequests = new ArrayList<>();
        CreateBlockRequest request = new CreateBlockRequest();
        request.setRule(target);
        request.setTargetType(BlockConstants.TargetType.valueOf(targetType));
        request.setFinalDecision(decision);
        request.setOperator("aliyun_risk");
        request.setRiskRequestId(requestId);
        request.setDeviceToken(deviceToken);
        if (BlockConstants.FinalDecision.BLOCK == decision) {
            Date date = DateUtil.addDayToDate(new Date(), Integer.valueOf(period));
            request.setEndTime(date);
            request.setTimePeriod(TimePeriodEnum.DAY);
        }
        if (BlockConstants.FinalDecision.PENDING == decision) {
            request.setNextStep(BlockConstants.NextStep.valueOf(nextStep));
            Date date = DateUtil.addDayToDate(new Date(), 360);
            request.setEndTime(date);
        }
        Arrays.stream(business.split(SPLIT)).forEach(scene -> {
            request.setBusiness(scene);
            createBlockRequests.add(request);
        });
        log.info("buildBlockBusiness target={}, targetType={}, result={}, business={}, period={}, nextStep={}, createBlockRequests={}", target, targetType, result, business, period, nextStep, createBlockRequests);
        return createBlockRequests;
    }

    @Override
    public String endpoint() {
        return kEventProperties.getAliyunRisk().get(EventConstants.AliyunRiskConfig.domain);
    }

}
