package com.kikitrade.kevent.dal.model;

import com.alicloud.openservices.tablestore.model.search.FieldType;
import com.kikitrade.framework.ots.annotations.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: penuel
 * @date: 2022/5/4 15:31
 * @desc: TODO
 */
@Data
@Table(name = "event")
public class EventDO implements Serializable {
    public static final String INDEX_EVENT_SEARCH = "idx_event_search";

    /**
     * 事件ID
     */
    @SearchIndex(name = INDEX_EVENT_SEARCH, column = "id")
    @PartitionKey(name = "id")
    private String id;
    /**
     * 事件名称
     */
    @SearchIndex(name = INDEX_EVENT_SEARCH, column = "name")
    @Column(name = "name")
    private String name;
    /**
     * 事件主体内容 - jsonString
     */
    @SearchIndex(name = INDEX_EVENT_SEARCH, column = "body")
    @Column(name = "body")
    private String body;
    /**
     * 事件发生时间
     */
    @SearchIndex(name = INDEX_EVENT_SEARCH, column = "time",fieldType = FieldType.LONG)
    @Column(name = "time", type = Column.Type.INTEGER)
    private Long time;
    /**
     * 非必须 - 全局事件幂等性唯一id
     * eg. orderId->用户入金事件
     */
    @SearchIndex(name = INDEX_EVENT_SEARCH, column = "uid")
    @Column(name = "uid")
    private String uid;

    @SearchIndex(name = INDEX_EVENT_SEARCH, column = "customer_id")
    @Column(name = "customer_id")
    private String customerId;
    /**
     * 用户设备ID
     */
    @SearchIndex(name = INDEX_EVENT_SEARCH, column = "device_id")
    @Column(name = "device_id")
    private String deviceId;

    @SearchIndex(name = INDEX_EVENT_SEARCH, column = "source")
    @Column(name = "source")
    private String source;
    @SearchIndex(name = INDEX_EVENT_SEARCH, column = "status")
    @Column(name = "status")
    private String status;

    @SearchIndex(name = INDEX_EVENT_SEARCH, column = "created", fieldType = FieldType.LONG)
    @Column(name = "created", type = Column.Type.INTEGER)
    private Date created;

    @SearchIndex(name = INDEX_EVENT_SEARCH, column = "modified", fieldType = FieldType.LONG)
    @Column(name = "modified", type = Column.Type.INTEGER)
    private Date modified;

    @Transient
    private String deviceToken;
    @Transient
    private String ip;
    @Transient
    private String callEvent;
}
