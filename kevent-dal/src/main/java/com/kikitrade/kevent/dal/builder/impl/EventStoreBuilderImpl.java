package com.kikitrade.kevent.dal.builder.impl;

import com.alicloud.openservices.tablestore.model.*;
import com.alicloud.openservices.tablestore.model.condition.ColumnCondition;
import com.alicloud.openservices.tablestore.model.condition.CompositeColumnValueCondition;
import com.alicloud.openservices.tablestore.model.condition.SingleColumnValueCondition;
import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.alicloud.openservices.tablestore.model.search.sort.FieldSort;
import com.alicloud.openservices.tablestore.model.search.sort.Sort;
import com.alicloud.openservices.tablestore.model.search.sort.SortOrder;
import com.kikitrade.framework.common.model.PageResult;
import com.kikitrade.framework.common.model.TokenPage;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import com.kikitrade.kevent.common.constant.EventConstants;
import com.kikitrade.kevent.dal.builder.EventStoreBuilder;
import com.kikitrade.kevent.dal.model.EventDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: penuel
 * @date: 2022/5/5 09:39
 * @desc: TODO
 */
@Component
@Slf4j
public class EventStoreBuilderImpl extends WideColumnStoreBuilder<EventDO> implements EventStoreBuilder {

    public static final String SUFFIX_STATUS = "_status";

    @PostConstruct
    public void init() {
        init(EventDO.class);
    }

    @Override
    public boolean save(EventDO event) {
        EventDO dbEvent = getRow(event);
        if (null != dbEvent) {
            return true;
        }
        return putRow(event, RowExistenceExpectation.EXPECT_NOT_EXIST);
    }

    @Override
    public boolean updateStatusByType(String eventId, EventConstants.DeliveryChannel deliveryChannel, EventConstants.DeliveryStatus deliveryStatus) {
        try {
            String deliveryStatusColumn = deliveryChannel.name().toLowerCase(Locale.ROOT) + SUFFIX_STATUS;

            Condition condition = new Condition(RowExistenceExpectation.EXPECT_EXIST);

            EventDO eventDO = new EventDO();
            eventDO.setId(eventId);
            eventDO.setModified(new Date());

            //动态列
            List<Column> extensionColumns = new ArrayList<>();
            extensionColumns.add(new Column(deliveryStatusColumn, ColumnValue.fromString(deliveryStatus.name())));

            //条件更新
            if (deliveryStatus == EventConstants.DeliveryStatus.PROCESSING) {
                CompositeColumnValueCondition compositeCondition = new CompositeColumnValueCondition(CompositeColumnValueCondition.LogicOperator.OR);
                ColumnCondition deliveryStatusCondition1 = new SingleColumnValueCondition(deliveryStatusColumn, SingleColumnValueCondition.CompareOperator.EQUAL, ColumnValue.fromString(""));
                ColumnCondition deliveryStatusCondition2 = new SingleColumnValueCondition(deliveryStatusColumn, SingleColumnValueCondition.CompareOperator.EQUAL, ColumnValue.fromString(EventConstants.DeliveryStatus.FAIL.name()));
                ColumnCondition deliveryStatusCondition3 = new SingleColumnValueCondition(deliveryStatusColumn, SingleColumnValueCondition.CompareOperator.EQUAL, ColumnValue.fromString(EventConstants.DeliveryStatus.PROCESSING.name()));
                ColumnCondition deliveryStatusCondition4 = new SingleColumnValueCondition(deliveryStatusColumn, SingleColumnValueCondition.CompareOperator.EQUAL, ColumnValue.fromString(EventConstants.DeliveryStatus.WAIT_CALL.name()));

                compositeCondition.addCondition(deliveryStatusCondition1);
                compositeCondition.addCondition(deliveryStatusCondition2);
                compositeCondition.addCondition(deliveryStatusCondition3);
                compositeCondition.addCondition(deliveryStatusCondition4);
                condition.setColumnCondition(compositeCondition);
            } else {
                ColumnCondition deliveryStatusCondition = new SingleColumnValueCondition(deliveryStatusColumn, SingleColumnValueCondition.CompareOperator.EQUAL, ColumnValue.fromString(EventConstants.DeliveryStatus.PROCESSING.name()));
                condition.setColumnCondition(deliveryStatusCondition);
            }

            return updateRow(eventDO, Arrays.asList(deliveryStatusColumn, "modified"), null, extensionColumns, condition);
        } catch (Exception e) {
            log.error("updateDeliveryStatus failed, event:{}, deliveryChannel:{}, deliveryStatus:{}", eventId, deliveryChannel, deliveryStatus, e);
            return false;
        }
    }

    @Override
    public EventConstants.DeliveryStatus getDeliveryStatusByType(String eventId, EventConstants.DeliveryChannel deliveryChannel) {
        EventDO eventDO = new EventDO();
        eventDO.setId(eventId);
        Map<String, Object> rowForMap = getRowForMap(eventDO);
        String deliveryStatusColumn = deliveryChannel.name().toLowerCase(Locale.ROOT) + SUFFIX_STATUS;
        if (null != rowForMap && null != rowForMap.get(deliveryStatusColumn)) {
            String status = rowForMap.get(deliveryStatusColumn).toString();
            return EventConstants.DeliveryStatus.valueOf(status);
        }
        return null;
    }

    @Override
    public TokenPage<EventDO> listEventByTime(Long start, Long end, EventConstants.DeliveryChannel deliveryChannel, EventConstants.DeliveryStatus deliveryStatus, String nextToken, int limit) {

        String deliveryStatusColumn = deliveryChannel.name().toLowerCase(Locale.ROOT) + SUFFIX_STATUS;
        BoolQuery query = QueryBuilders.bool()
                .must(QueryBuilders.term(deliveryStatusColumn, deliveryStatus.name()))
                .must(QueryBuilders.bool().filter(QueryBuilders.range("time")
                        .greaterThan(start)))
                .must(QueryBuilders.bool().filter(QueryBuilders.range("time")
                        .lessThanOrEqual(end)))
                .build();
        FieldSort fieldSort = new FieldSort("time");//时间升序
        fieldSort.setOrder(SortOrder.ASC);
        Sort sort = new Sort(Arrays.asList(fieldSort));

        TokenPage<EventDO> tokenPage = pageSearchQuery(query, sort, nextToken, limit, EventDO.INDEX_EVENT_SEARCH);
        log.info("getEventByTime:offset:{}, limit:{}, tokenPage:{}", nextToken, limit, tokenPage);
        return tokenPage;
    }

    @Override
    public TokenPage<EventDO> filterEventByTime(Long start, Long end, EventConstants.DeliveryChannel deliveryChannel, EventConstants.DeliveryStatus deliveryStatus, List<EventConstants.EventName> eventNameList, String nextToken, int limit) {
        String deliveryStatusColumn = deliveryChannel.name().toLowerCase(Locale.ROOT) + SUFFIX_STATUS;
        BoolQuery query = QueryBuilders.bool()
                .must(QueryBuilders.term(deliveryStatusColumn, deliveryStatus.name()))
                .must(QueryBuilders.terms("name").terms(eventNameList.stream().map(eventName -> eventName.getName()).toArray()))
                .must(QueryBuilders.bool().filter(QueryBuilders.range("time")
                        .greaterThan(start)))
                .must(QueryBuilders.bool().filter(QueryBuilders.range("time")
                        .lessThanOrEqual(end)))
                .build();
        FieldSort fieldSort = new FieldSort("time");//时间升序
        fieldSort.setOrder(SortOrder.ASC);
        Sort sort = new Sort(Arrays.asList(fieldSort));

        TokenPage<EventDO> tokenPage = pageSearchQuery(query, sort, nextToken, limit, EventDO.INDEX_EVENT_SEARCH);
        log.info("filterEventByTime:offset:{}, limit:{}, tokenPage:{}", nextToken, limit, tokenPage);
        return tokenPage;
    }

    @Override
    public TokenPage<EventDO> filterEventByCreated(Long end, EventConstants.DeliveryChannel deliveryChannel, List<EventConstants.DeliveryStatus> deliveryStatus, List<EventConstants.EventName> eventNameList, String nextToken, int limit) {
        String deliveryStatusColumn = deliveryChannel.name().toLowerCase(Locale.ROOT) + SUFFIX_STATUS;
        BoolQuery query = QueryBuilders.bool()
                .must(QueryBuilders.terms(deliveryStatusColumn).terms(deliveryStatus.stream().map(status -> status.name()).toArray()))
                .must(QueryBuilders.terms("name").terms(eventNameList.stream().map(eventName -> eventName.getName()).toArray()))
                .must(QueryBuilders.bool().filter(QueryBuilders.range("created")
                        .lessThanOrEqual(end)))
                .build();
        FieldSort fieldSort = new FieldSort("created");
        fieldSort.setOrder(SortOrder.DESC);
        Sort sort = new Sort(Arrays.asList(fieldSort));

        return pageSearchQuery(query, sort, nextToken, limit, EventDO.INDEX_EVENT_SEARCH);
    }

    @Override
    public List<EventDO> filterEventByCustomerId(String customerId, EventConstants.DeliveryChannel deliveryChannel, EventConstants.DeliveryStatus deliveryStatus, EventConstants.EventName eventName) {
        String deliveryStatusColumn = deliveryChannel.name().toLowerCase(Locale.ROOT) + SUFFIX_STATUS;
        BoolQuery query = QueryBuilders.bool()
                .must(QueryBuilders.term("customer_id", customerId))
                .must(QueryBuilders.term("name", eventName.getName()))
                .must(QueryBuilders.term(deliveryStatusColumn, deliveryStatus.name()))
                .build();
        FieldSort fieldSort = new FieldSort("time");//时间升序
        fieldSort.setOrder(SortOrder.DESC);
        Sort sort = new Sort(Arrays.asList(fieldSort));

        PageResult result = pageSearch(query, sort, 0, 1, EventDO.INDEX_EVENT_SEARCH);
        return result != null ? result.getRows() : null;
    }

    /**
     * 删除事件
     *
     * @param eventIds
     * @return
     */
    @Override
    public boolean delete(List<String> eventIds) {
        List<EventDO> eventDOList = eventIds.stream()
                .map(eventId -> {
                    EventDO eventDO = new EventDO();
                    eventDO.setId(eventId);
                    return eventDO;
                })
                .collect(Collectors.toList());
        return batchDeleteRows(eventDOList);
    }

}
