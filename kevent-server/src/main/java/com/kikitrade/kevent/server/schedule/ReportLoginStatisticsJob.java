package com.kikitrade.kevent.server.schedule;

import com.kikitrade.framework.common.model.TokenPage;
import com.kikitrade.kevent.common.constant.EventConstants;
import com.kikitrade.kevent.common.util.DateUtil;
import com.kikitrade.kevent.dal.builder.EventStoreBuilder;
import com.kikitrade.kevent.dal.model.EventDO;
import com.kikitrade.kevent.server.channel.AliyunRiskTrackingExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.elasticjob.api.ShardingContext;
import org.apache.shardingsphere.elasticjob.simple.job.SimpleJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: wang
 * @date: 2023/3/14
 * @desc: 离线数据上报job 7天内登录过用户
 */
@Slf4j
@Component
public class ReportLoginStatisticsJob implements SimpleJob {
    @Autowired
    @Lazy
    private EventStoreBuilder eventStoreBuilder;
    @Autowired(required = false)
    @Lazy
    AliyunRiskTrackingExecutor aliyunRiskTrackingExecutor;

    @Override
    public void execute(ShardingContext shardingContext) {
        Long dateEnd = System.currentTimeMillis();
        Long dateStart = DateUtil.addDayToDate(new Date(dateEnd), -8).getTime();
        List<EventConstants.EventName> reportEventList = new ArrayList<>();
        reportEventList.add(EventConstants.EventName.LOGIN);
        log.info("ReportLoginStatisticsJob execute start, dateStart {}, dateEnd {}, reportEventList {}", dateStart, dateEnd, reportEventList);
        reportUserStatistics(dateStart, dateEnd, reportEventList);
    }

    private void reportUserStatistics(Long dateStart, Long dateEnd, List<EventConstants.EventName> reportEventList) {
        try {
            String nextToken = "";
            Set<String> alreadyReportedCustomerIds = new HashSet<>();
            while (true) {
                TokenPage<EventDO> eventDOTokenPage = eventStoreBuilder.filterEventByTime(dateStart, dateEnd, EventConstants.DeliveryChannel.ALIYUN_RISK, EventConstants.DeliveryStatus.SUCCESS, reportEventList, nextToken, 100);
                if (eventDOTokenPage == null || eventDOTokenPage.getRows() == null || eventDOTokenPage.getRows().size() <= 0) {
                    break;
                }
                Set<String> customerIds = eventDOTokenPage.getRows().stream().collect(Collectors.groupingBy(EventDO::getCustomerId)).keySet();
                customerIds.forEach(uid -> {
                    log.info("ReportLoginStatisticsJob reportUserStatistics customerId {}", uid);
                    if (!alreadyReportedCustomerIds.contains(uid)) {
                        aliyunRiskTrackingExecutor.reportUserStatistics(uid);
                    }
                });
                alreadyReportedCustomerIds.addAll(customerIds);
                nextToken = eventDOTokenPage.getNextToken();
                if (nextToken == null) {
                    break;
                }
            }
            alreadyReportedCustomerIds.clear();
        } catch (Exception e) {
            log.error("ReportLoginStatisticsJob reportUserStatistics error", e);
        }
    }
}
