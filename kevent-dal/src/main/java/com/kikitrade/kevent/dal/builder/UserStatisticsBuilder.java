package com.kikitrade.kevent.dal.builder;

import com.kikitrade.kevent.dal.model.UserStatisticsDO;

/**
 * User statistics ots accessor
 *
 * <AUTHOR>
 * @create 2022/6/8 5:12 下午
 * @modify
 */
public interface UserStatisticsBuilder {
    /**
     * 获取用户某天
     *
     * @param customerId
     * @param acStaticDate
     * @return
     */
    UserStatisticsDO get(String customerId, String acStaticDate);

    UserStatisticsDO getLatest(String customerId);
}
