package com.kikitrade.kevent.server.schedule;

import com.kikitrade.framework.common.model.TokenPage;
import com.kikitrade.kevent.common.constant.EventConstants;
import com.kikitrade.kevent.dal.builder.EventStoreBuilder;
import com.kikitrade.kevent.dal.model.EventDO;
import com.kikitrade.kevent.server.autoconfigure.KEventProperties;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.elasticjob.api.ShardingContext;
import org.apache.shardingsphere.elasticjob.simple.job.SimpleJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc Event cleanup job that runs periodically to clean up old events.
 * @date 2025/01/03 16:01
 */

@Slf4j
@Component
public class EventCleanupJob implements SimpleJob {

    @Autowired
    @Lazy
    private EventStoreBuilder eventStoreBuilder;

    @Resource
    private KEventProperties kEventProperties;

    @Override
    public void execute(ShardingContext shardingContext) {
        int clearEventDays = kEventProperties.getClearEventDays();
        Calendar checkpoint = Calendar.getInstance();
        checkpoint.add(Calendar.DAY_OF_YEAR, -clearEventDays);
        Long dateEnd = checkpoint.getTimeInMillis();

        log.info("EventCleanupJob execute start, dateEnd: {}", dateEnd);

        List<EventConstants.DeliveryStatus> deliveryStatusList = List.of(
                EventConstants.DeliveryStatus.SUCCESS,
                EventConstants.DeliveryStatus.FAIL
        );

        List<EventConstants.EventName> eventNames = List.of(
                EventConstants.EventName.LIKE,
                EventConstants.EventName.LIKED,
                EventConstants.EventName.FOLLOW,
                EventConstants.EventName.FOLLOWED
        );

        String nextToken = "";
        int totalDeleted = 0;
        int currentBatchDeleted = 0;
        do {
            try {
                TokenPage<EventDO> tokenPage = eventStoreBuilder.filterEventByCreated(dateEnd, EventConstants.DeliveryChannel.ACTIVITY, deliveryStatusList, eventNames, nextToken, 100);
                if (tokenPage == null || tokenPage.getRows() == null || tokenPage.getRows().isEmpty()) {
                    log.info("No more events to process.");
                    break;
                }

                List<String> eventIds = tokenPage.getRows().stream()
                        .map(EventDO::getId)
                        .collect(Collectors.toList());
                log.info("Reday to delete events: {}" , eventIds);

                if (!eventIds.isEmpty()) {
                    boolean deleteSuccess = eventStoreBuilder.delete(eventIds);
                    if (deleteSuccess) {
                        currentBatchDeleted += eventIds.size();
                        totalDeleted += eventIds.size();
                        log.info("Successfully deleted events: {}", eventIds);
                        log.info("Successfully currentBatchDeleted: {},totalDeleted: {}",currentBatchDeleted,totalDeleted);

                        if (currentBatchDeleted >= 100) {
                            Thread.sleep(2000);
                            currentBatchDeleted = 0;
                        }

                        if (totalDeleted >= 500) {
                            log.info("Reached maximum deletion limit of 500 rows for this execution.");
                            break;
                        }
                    } else {
                        log.info("Failed to delete events: {}", eventIds);
                    }
                }

                nextToken = tokenPage.getNextToken();
            } catch (Exception e) {
                log.error("Error occurred during event cleanup job execution", e);
                break;
            }
        } while (nextToken != null);

        log.info("EventCleanupJob execute end, total deleted: {}", totalDeleted);
    }
}