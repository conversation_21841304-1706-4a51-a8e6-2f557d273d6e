package com.kikitrade.kevent.server;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.credentials.AlibabaCloudCredentials;
import com.aliyun.credentials.provider.DefaultCredentialsProvider;
import com.aliyun.credentials.utils.AuthUtils;
import com.aliyun.credentials.utils.StringUtils;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.auth.AlibabaCloudCredentialsProvider;
import com.aliyuncs.auth.BasicCredentials;
import com.aliyuncs.auth.ECSMetadataServiceCredentialsFetcher;
import com.aliyuncs.auth.InstanceProfileCredentials;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.exceptions.ServerException;
import com.aliyuncs.http.FormatType;
import com.aliyuncs.http.HttpClientConfig;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.http.ProtocolType;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.saf.model.v20190521.ExecuteRequestRequest;
import com.aliyuncs.saf.model.v20190521.ExecuteRequestResponse;
import com.kikitrade.kevent.dal.model.UserStatisticsDO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2022/6/8 10:07 上午
 * @modify
 */
@Slf4j
public class RiskTest {

    public static final String AK = "xxx";
    public static final String SK = "yyy";


    @Test
    public void test() {
        // 创建DefaultAcsClient实例并初始化，仅需初始化一遍
        DefaultProfile profile = DefaultProfile.getProfile(
                "ap-southeast-1",          // 地域ID
                AK,      // RAM账号的AccessKey ID
                SK); // RAM账号Access Key Secret
        // http连接池相关配置
        HttpClientConfig clientConfig = HttpClientConfig.getDefault();
        clientConfig.setMaxRequestsPerHost(6);
        clientConfig.setMaxIdleConnections(20);
        // http超时时间配置
        clientConfig.setReadTimeoutMillis(10000);
        clientConfig.setConnectionTimeoutMillis(3000);
        profile.setHttpClientConfig(clientConfig);
        IAcsClient client = new DefaultAcsClient(profile);


        //发起请求
        ExecuteRequestRequest executeRequestRequest = new ExecuteRequestRequest();
        //如果需要指定特殊版本号，可以在这里调整
        //executeRequestRequest.setVersion(version);
        // 指定请求方法
        executeRequestRequest.setSysMethod(MethodType.POST);
        //指定协议，目前只支持HTTPS
        executeRequestRequest.setSysProtocol(ProtocolType.HTTPS);
        //服务的产品码：address_validation/email_risk/coupon_abuse/account_abuse等
        String service = "目标产品的service";
        executeRequestRequest.setService("device_risk");

        // 业务详细参数，具体见文档里的业务参数部分,不需要的参数就不需要设置
        Map<String, Object> serviceParams = new HashMap<String, Object>();

//        // IMEI
//        serviceParams.put("imei", "A000******A025");
//        //手机号13********3
//        serviceParams.put("mobile", "13********3");
        serviceParams.put("uaToken", "140#BfMoS/xbzzPL5Qo23byb3pSjb06sYietnv6wy14Pxf/mXHS18T5UCi382oEuIrobwiMQOo1LF6hqzzn02Qvfr52zzqViba7ulFzx2DD3VthqzFr/HX82ltQzaIziVXE/YFr5Kbk0PtrPHpcoa6hu5LLS5277YyTEQvqOhp71/3azQvzxy8/sg0a2KkPMb18Q6J3lvNd1EHMzwXc51h6rAD9oLH6ew0Nj+ZGXPyVgg5gQy76JeF8kdpyUmgRG13ObEmALeiJPuH5z+qFDb3wFk24IuYZqS87D6rpkLoQEEG1E5IyDVy9TDCeUwhDJ3fXJrEpOtN98fg3iI3yMtFWv3t9cPnmgRDTRt62wLTKBMMuDY2MV2QgkTv+EsyhEc1oOz0z7GVAzHy9u/llW7T1+weJ2rEiCUniYQ/IRFCabKsYlvXrZuxlbMk7x0Hi9c+KdZwGxTMvTgsbW4hck5wJPqwh0HWLoASJUOBqXmN3f7QDB25Y5p82RktXu5w2A2gS23wTzedJYdQVRafJj37vxey+0+Xyd6iv3iSFEyQiy+BObIQrWjQkbiltQCMcRhIfAyzNAi4kPeWLf+clkQyFmvw320cMVRUzHBl+7m1QmOn5R8M0C15cVacbCFQH/+UJDwy9+oiqOsFjIW06Ovtc7QeuO0iv8JFopkJI+CJxCQaDTJwvbPsl3il9p8Lt0hDZC4RjfucyeOrezflpGbA5ERektsOsy8iu65qXWErfY9MiGMP2n7ghNjzhIzlMjuqi7bkORsoeLDsqrWJCxNg/fa8uOemYrQ9Ex428qpdpOR0m7SzXSkTyM37AHhAJcrykS6CO7JG2OmODddmfNgvjDi1hSNUlM/KROCX3NruawlnUFlz8Xuf5ZoW/YC8ex0XGdZPLqvkGU762Finkp0+vUctiYAOZEY050HJFlqyWdvlQ5S/F2GUjL2KEc2DEHsiG9AAYQcwJQE/FYSSLDhb==");
        serviceParams.put("webUmidToken","T2gAaNs96g7vRM4ly6qCCbMxiDkdNd1iEALQISBGI9TNjKqQPHm7bShtR2YCMf3SrmE=");
        executeRequestRequest.setServiceParameters(JSONObject.toJSONString(serviceParams));
        executeRequestRequest.setAcceptFormat(FormatType.JSON);
        try {
            ExecuteRequestResponse httpResponse = client.getAcsResponse(executeRequestRequest);
            System.out.println("httpResponse:" + JSONObject.toJSONString(httpResponse));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testA() throws Exception {
        // AccessKeyId AccessKeySecret

        //endpoint slb IP
        String endpoint = "http://*************";
        String regionId = "ap-southeast-1";

        //
        SHPLClient.SHPLHttpClientConfig clientConfig = new SHPLClient.SHPLHttpClientConfig();
        //
        clientConfig.setMaxRequests(400);
        clientConfig.setMaxRequestsPerHost(200);
        //
        clientConfig.setConnectionRequestTimeout(1500);
        clientConfig.setConnectTimeout(1500);
        clientConfig.setSocketTimeout(1500);

        // SHPLClient Client Token
        SHPLClient shplClient = SHPLClient.getInstance(endpoint, regionId, AK, SK, clientConfig);

        //
        SHPLClient.SHPLRequest shplRequest = new SHPLClient.SHPLRequest();
        // Service saf_de
        shplRequest.setService(SHPLClient.DECISION_SERVICE);
        // code
        shplRequest.setEventCode("de_ayefij7618");
        //
        Map<String, Object> serviceParameters = new HashMap(4);
        serviceParameters.put("DEcustomerId", "allenP");
        serviceParameters.put("price", 50000);
        shplRequest.setServiceParameters(serviceParameters);

        // json
        SHPLClient.SHPLResponse result = shplClient.getSHPLResponse(shplRequest);
        System.out.println(" " + JSONObject.toJSONString(result));
    }

    @Test
    public void testField() throws NoSuchFieldException, IllegalAccessException {
        UserStatisticsDO userStatisticsDO = new UserStatisticsDO();
        userStatisticsDO.setCustomerId("123");
        userStatisticsDO.setAcAum(BigDecimal.ONE);

        Class<? extends UserStatisticsDO> clazz = userStatisticsDO.getClass();
        clazz.getFields();

        String customerId = (String) clazz.getField("customerId").get(userStatisticsDO);
        BigDecimal acAum = (BigDecimal) clazz.getField("acAum").get(userStatisticsDO);

        System.out.println(customerId);
        System.out.println(acAum);
    }

    @Test
    public void testRisk2() {
        DefaultProfile profile = DefaultProfile.getProfile("ap-southeast 1", AK, SK);
        IAcsClient client = new DefaultAcsClient(profile);
        CommonRequest request = new CommonRequest();
        request.setSysMethod(MethodType.POST);
        request.setSysDomain("*************/requestPublicByToken.do?");
        request.setSysVersion("2019-05-21");
        request.setSysAction("RequestDecision");
        request.putQueryParameter("RegionId", "ap-southeast-1");
        request.putBodyParameter("ServiceParameters", "{\"eventCode\":\"de_ayefij7618\",\"DEamount\":\"100\",\"DEacCircleAccumulatedAmountWithdraw\":\"1000\",\"DEcustomerId\":\"allenP\"}");
        try {
            CommonResponse response = client.getCommonResponse(request);
            System.out.println(response.getData());
            JSONObject obj = JSON.parseObject(response.getData());
            JSONObject data = obj.getJSONObject("Data");
            Double score = data.getDouble("score");
            String finalDecision = data.getString("finalDecision");
            String tags = data.getString("tags");
            System.out.println(score);
            System.out.println(finalDecision);
            System.out.println(tags);

        } catch (ServerException e) {
            e.printStackTrace();
        } catch (ClientException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testRisk() {

        System.out.println("test start*****************");
        IAcsClient client = getDefaultAcsClient();

        CommonRequest request = new CommonRequest();
        request.setSysMethod(MethodType.POST);
        request.setSysDomain("saf.ap-southeast-1.aliyuncs.com");
        request.setSysVersion("2019-05-21");
        request.setSysAction("RequestDecision");
        request.putQueryParameter("RegionId", "ap-southeast-1");
        request.putBodyParameter("ServiceParameters", "{\"eventCode\":\"de_aflucw5770\",\"DEamount\":\"100\",\"DEacCircleAccumulatedAmountWithdraw\":\"1000\",\"DEcustomerId\":\"allenP\"}");
        try {
            CommonResponse response = client.getCommonResponse(request);
            System.out.println("**********************************************");
            System.out.println(response.getData());
        } catch (ServerException e) {
            e.printStackTrace();
        } catch (ClientException e) {
            e.printStackTrace();
        }

        System.out.println("test end*****************");
    }

    public IAcsClient getDefaultAcsClient() {

        IAcsClient acsClient = null;
        String ramRole = AuthUtils.getEnvironmentECSMetaData();

        if (StringUtils.isEmpty(ramRole)) {
            AlibabaCloudCredentials credentials = (new DefaultCredentialsProvider()).getCredentials();
            BasicCredentials baseCredentials = new BasicCredentials(credentials.getAccessKeyId(), credentials.getAccessKeySecret());
            DefaultProfile profile = DefaultProfile.getProfile("ap-southeast 1", credentials.getAccessKeyId(), credentials.getAccessKeySecret());
            acsClient = new DefaultAcsClient(profile);

            log.info("getDefaultAcsClient key:{}, secret:{}", null == baseCredentials.getAccessKeyId() ? "null" : (baseCredentials.getAccessKeyId().substring(0, 3) + "***"), null == baseCredentials.getAccessKeySecret() ? "null" : (baseCredentials.getAccessKeySecret().substring(0, 3) + "***"));
        } else {
            RiskTest.CustomInstanceProfileCredentialsProvider provider = new RiskTest.CustomInstanceProfileCredentialsProvider(ramRole);
            DefaultProfile profile = DefaultProfile.getProfile();
            acsClient = new DefaultAcsClient(profile, provider);
            log.info("getDefaultAcsClient role:{}", ramRole);
        }
        return acsClient;
    }

    public class CustomInstanceProfileCredentialsProvider implements AlibabaCloudCredentialsProvider {

        private InstanceProfileCredentials credentials = null;
        public int ecsMetadataServiceFetchCount = 0;
        private ECSMetadataServiceCredentialsFetcher fetcher;
        private static final int MAX_ECS_METADATA_FETCH_RETRY_TIMES = 3;
        private int maxRetryTimes = MAX_ECS_METADATA_FETCH_RETRY_TIMES;
        private final String roleName;

        public CustomInstanceProfileCredentialsProvider(String roleName) {
            if (null == roleName) {
                throw new NullPointerException("You must specifiy a valid role name.");
            }
            this.roleName = roleName;
            this.fetcher = new ECSMetadataServiceCredentialsFetcher();
            this.fetcher.setRoleName(this.roleName);
        }

        @Override
        public com.aliyuncs.auth.AlibabaCloudCredentials getCredentials() throws ClientException {
            if (credentials == null) {
                ecsMetadataServiceFetchCount += 1;
                credentials = fetcher.fetch(maxRetryTimes);
            } else if (credentials.isExpired()) {
                credentials = fetcher.fetch(maxRetryTimes);
                log.warn("SDK.SessionTokenExpired", "Current session token has expired.");
            } else if (credentials.willSoonExpire() && credentials.shouldRefresh()) {
                try {
                    ecsMetadataServiceFetchCount += 1;
                    credentials = fetcher.fetch();
                } catch (ClientException e) {
                    // Use the current expiring session token and wait for next round
                    credentials.setLastFailedRefreshTime();
                }
            }
            return credentials;
        }
    }
}
