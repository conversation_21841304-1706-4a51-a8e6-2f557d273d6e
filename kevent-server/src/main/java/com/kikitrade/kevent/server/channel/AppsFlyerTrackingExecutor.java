package com.kikitrade.kevent.server.channel;


import com.alibaba.fastjson.JSONObject;
import com.kikitrade.kcustomer.api.model.DeviceInfoDTO;
import com.kikitrade.kevent.common.constant.EventConstants;
import com.kikitrade.kevent.common.constant.EventConstants.AppsFlyerConfig;
import com.kikitrade.kevent.common.constant.EventConstants.AppsFlyerConstant;
import com.kikitrade.kevent.common.constant.EventConstants.EventBodyConstant;
import com.kikitrade.kevent.common.constant.EventMessageEnum;
import com.kikitrade.kevent.common.exception.EventException;
import com.kikitrade.kevent.common.util.DateUtil;
import com.kikitrade.kevent.dal.builder.EventStoreBuilder;
import com.kikitrade.kevent.dal.model.EventDO;
import com.kikitrade.kevent.server.autoconfigure.KEventProperties;
import com.kikitrade.kevent.server.executor.impl.TrackingExecutor;
import com.kikitrade.kevent.server.util.TwoTuple;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

@Component("appsFlyerTrackingExecutor")
@Slf4j
@ConditionalOnProperty(name = {"kevent.apps-flyer.api_url"})
public class AppsFlyerTrackingExecutor extends TrackingExecutor {

    @Resource
    private KEventProperties kEventProperties;

    /**
     * 设备OS类型枚举
     */
    String androidDeviceType = "android";
    String iosDeviceType = "ios";
    /**
     * 线上环境变量
     */
    String prod = "prod";

    @Autowired
    public EventStoreBuilder eventStoreBuilder;

    @Override
    public boolean isContinue(EventDO input) {
        boolean result = super.isContinue(input);
        if (result) {
            Set<String> keySet = kEventProperties.getAppsFlyerEventName().keySet();
            if (keySet.parallelStream().anyMatch(k -> regularCheck(k, parseOriginalName(input.getName()).getFirst()))) {
                return true;
            }
            return false;
        }
        return false;
    }

    @Override
    public Map<String, Object> mapping(EventDO event) throws EventException {
        //查询设备信息
        DeviceInfoDTO deviceInfo = getDeviceInfoByCustomerIdOrDeviceId(event.getCustomerId(),event.getDeviceId());
        //校验并设置参数
        Map<String, Object> postParameters = new HashMap<>();
        if(StringUtils.isEmpty(deviceInfo.getAdId())){
            throw new EventException(EventMessageEnum.SYSTEM_DATA_NOT_FOUND);
        }
        postParameters.put(AppsFlyerConstant.appsflyerId, deviceInfo.getAdId());
        postParameters.put(AppsFlyerConstant.advertisingId, deviceInfo.getDeviceId());
        postParameters.put(AppsFlyerConstant.customerUserId, event.getCustomerId());
        if (StringUtils.isNotBlank(deviceInfo.getAppVersion())) {
            postParameters.put(AppsFlyerConstant.appVersionName, deviceInfo.getAppVersion());
        }
        postParameters.put(AppsFlyerConstant.eventName, getDefinedEventName(event.getName()));
        //建议参数
        if (StringUtils.isNotBlank(deviceInfo.getIp())) {
            postParameters.put(AppsFlyerConstant.ip, deviceInfo.getIp());
        }
        if (Objects.nonNull(event.getTime())) {
            postParameters.put(AppsFlyerConstant.eventTime, DateUtil.date2UTC(event.getTime()));
        }
        if (Objects.nonNull(deviceInfo.getOsVersion())) {
            postParameters.put(AppsFlyerConstant.os, deviceInfo.getOsVersion());
        }

        JSONObject eventBody = JSONObject.parseObject(event.getBody());

        //如果是入金，将对应金额转为法币
        if (Objects.nonNull(eventBody)
                && Objects.nonNull(eventBody.get(EventBodyConstant.revenue))
                && Objects.nonNull(eventBody.get(EventBodyConstant.currency))) {
            String revenue = String.valueOf(eventBody.get(EventBodyConstant.revenue));
            String currency = String.valueOf(eventBody.get(EventBodyConstant.currency));
            String contentId = String.valueOf(eventBody.get(EventBodyConstant.contentId));
            String numItems = String.valueOf(eventBody.get(EventBodyConstant.numItems));
            String contentType = String.valueOf(eventBody.get(EventBodyConstant.contentType));
            String fiatRevenue = calFiatCurrency(revenue, currency);

            //跟踪收入参数
            postParameters.put(AppsFlyerConstant.eventCurrency, fiatCurrency);
            // postParameters.add(AppsFlyerConstant.eventRevenue, fiatRevenue);

            JSONObject jo = new JSONObject();

            jo.put(AppsFlyerConstant.eventValue_afRevenue, fiatRevenue);
            jo.put(AppsFlyerConstant.eventValue_afContentType, contentType);
            jo.put(AppsFlyerConstant.eventValue_afContentId, contentId);
            jo.put(AppsFlyerConstant.eventValue_afQuantity, numItems);

            //回传参数
            postParameters.put(AppsFlyerConstant.eventValue, jo.toJSONString());
        }

        return postParameters;
    }

    @Override
    public EventConstants.DeliveryStatus track(Map<String, Object> params) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        headers.add("authentication", getAppsFlyerEventToken().get(AppsFlyerConfig.androidAppToken));
        try {
            return apiCall(HttpMethod.POST, EventConstants.DeliveryStatus.class, params, headers);
        } catch (Exception e) {
            log.warn("AppsFlyer track data error", e);
            return EventConstants.DeliveryStatus.FAIL;
        }
    }

    @Override
    public String endpoint() {
        return getAppsFlyerConfig().get(AppsFlyerConfig.apiUrl);
    }

    @Override
    public EventConstants.DeliveryChannel deliveryChannel() {
        return EventConstants.DeliveryChannel.APPSFLYER;
    }

    Map<String, String> getAppsFlyerConfig() {
        return kEventProperties.getAppsFlyer();
    }

    Map<String, String> getAppsFlyerEventToken() {
        return kEventProperties.getAppsFlyerEventToken();
    }

    /**
     * 自定义事件名映射
     *
     * @param eventName
     * @return
     */
    protected String getDefinedEventName(String eventName) {
        TwoTuple<String, String> twoTuple = parseOriginalName(eventName);
        //原始事件名称
        String preName = twoTuple.getFirst();
        //后缀
        String suffix = twoTuple.getSecond();
        String definedEventName = getAppsFlyerEventName().get("__" + preName + "__" + suffix);
        if (StringUtils.isNotBlank(definedEventName)) {
            eventName = definedEventName;
        }
        return eventName;
    }

    Map<String, String> getAppsFlyerEventName() {
        return kEventProperties.getAppsFlyerEventName();
    }

}
