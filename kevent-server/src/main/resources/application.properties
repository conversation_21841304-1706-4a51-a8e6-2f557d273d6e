spring.application.name=kevent

logging.level.root=INFO
logging.pattern.file=[%p][%t][%d{yyyy-MM-dd HH:mm:ss.SSS}][%c][%L][%X{traceId}][%X{spanId}]%m%n
logging.pattern.console=[%p][%t][%d{yyyy-MM-dd HH:mm:ss.SSS}][%c][%L][%X{traceId}][%X{spanId}]%m%n
logging.file.path=./logs
logging.file.name=kevent.log

management.server.port=9090
management.health.defaults.enabled=false
management.endpoint.health.show-details=always
management.endpoints.migrate-legacy-ids=true

management.endpoints.web.exposure.include=env,health,info,httptrace,metrics,heapdump,threaddump,prometheus,dubbo
management.health.db.enabled=false
management.metrics.tags.application=kevent

management.metrics.dubbo.enabled=true
management.metrics.dubbo.percentiles-histogram=true
management.metrics.dubbo.percentiles=0.9, 0.99
management.metrics.druid.enable=true

#spring.profiles.active=local


management.health.elasticsearch.enabled=false
management.health.mongo.enabled=false

dubbo.metrics.protocol=prometheus
dubbo.metrics.aggregation.enabled=true
dubbo.metrics.prometheus.exporter.enabled=true
management.endpoint.dubbo.enabled=true
