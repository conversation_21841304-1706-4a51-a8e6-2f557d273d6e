package com.kikitrade.kevent.client.autoconfigure;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Map;


/**
 * @author: penuel
 * @date: 2022/5/4 15:13
 * @desc: TODO
 */
@ConfigurationProperties(prefix = "kiki.event")
@Data
public class EventProperties {

    private String topic;

    private String env = "prod";
    private String aliyunRiskTokenEventCode;
    private String aliyunRiskDomain;
    private String aliyunRiskRegion;
    private String aliyunRiskAk;
    private String aliyunRiskSk;
    private Map<String, String> questsNotifyUrl;
}
