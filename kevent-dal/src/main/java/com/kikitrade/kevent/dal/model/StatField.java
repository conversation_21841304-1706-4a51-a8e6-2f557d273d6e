package com.kikitrade.kevent.dal.model;

import java.lang.annotation.*;

import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * Field
 *
 * <AUTHOR>
 * @create 2022/8/11 9:09 下午
 * @modify
 */
@Documented
@Inherited
@Target(ElementType.FIELD)
@Retention(RUNTIME)
public @interface StatField {

    String name() default "";

    boolean include() default false;

    StatField.Type type() default Type.STRING;

    enum Type {
        /**
         * Default use attribute type
         */
        STRING,
        /**
         * 64位整数。
         */
        DECIMAL,
        /**
         * 浮点数。
         */
        INTEGER;
    }

}

