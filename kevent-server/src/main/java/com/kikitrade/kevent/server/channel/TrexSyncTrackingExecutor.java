package com.kikitrade.kevent.server.channel;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kikitrade.kevent.common.constant.EventConstants;
import com.kikitrade.kevent.common.exception.EventException;
import com.kikitrade.kevent.dal.model.EventDO;
import com.kikitrade.kevent.server.autoconfigure.KEventProperties;
import com.kikitrade.kevent.server.executor.impl.TrackingExecutor;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/7/22 14:59
 * @description: TREX同步跟踪执行器
 */
@Slf4j
@Component("trexSyncTrackingExecutor")
@ConditionalOnProperty(name = "kevent.activity-topic-name")
public class TrexSyncTrackingExecutor extends TrackingExecutor {

    @Resource
    private KEventProperties kEventProperties;

    /**
     * 映射事件数据到TREX所需格式
     *
     * @param event 事件数据
     * @return 映射后的参数
     * @throws EventException 事件异常
     */
    @Override
    public Map<String, Object> mapping(EventDO event) throws EventException {
        log.info("TrexSyncTrackingExecutor mapping event {}", event);
        String body = event.getBody();
        if (StringUtils.isBlank(body)) {
            return null;
        }

        JSONObject object = JSON.parseObject(body);
        JSONObject bodyObject = JSON.parseObject(object.getString("body"));
        Map<String, Object> data = new HashMap<>();

        data.put("customerId", event.getCustomerId());
        // 从事件体中提取必要字段
        if (bodyObject.containsKey("platform")) {
            data.put("platform", bodyObject.getString("platform"));
        }

        if (bodyObject.containsKey("socialId")) {
            data.put("socialId", bodyObject.getString("socialId"));
        }

        if (bodyObject.containsKey("socialHandleName")) {
            data.put("socialHandleName", bodyObject.getString("socialHandleName"));
        }

        if (bodyObject.containsKey("socialProfileImage")) {
            data.put("socialProfileImage", bodyObject.getString("socialProfileImage"));
        }

        if (bodyObject.containsKey("socialEmail")) {
            data.put("socialEmail", bodyObject.getString("socialEmail"));
        }

        log.info("TrexSyncTrackingExecutor mapped data: {}", JSON.toJSONString(data));
        return data;
    }

    /**
     * 发送HTTP POST请求到TREX服务
     *
     * @param params 请求参数
     * @return 投递状态
     */
    @Override
    public EventConstants.DeliveryStatus track(Map<String, Object> params) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");

        // 从配置中获取认证信息
        String apiKey = kEventProperties.getTrexSync().get("app_key");
        if (StringUtils.isNotBlank(apiKey)) {
            headers.add("app_key", apiKey);
        }

        try {
            return apiCall(HttpMethod.POST, EventConstants.DeliveryStatus.class, params, headers);
        } catch (Exception e) {
            log.warn("TrexSyncTrackingExecutor track data error", e);
            return EventConstants.DeliveryStatus.FAIL;
        }
    }

    /**
     * 获取TREX API端点
     *
     * @return API端点URL
     */
    @Override
    public String endpoint() {
        return kEventProperties.getTrexSync().get("host") + "/v1/s2s/customers/bindSocialInfo";
    }

    /**
     * 获取投递渠道
     *
     * @return TREX同步渠道
     */
    @Override
    public EventConstants.DeliveryChannel deliveryChannel() {
        return EventConstants.DeliveryChannel.TREX_SYNC;
    }

    /**
     * 判断是否继续处理事件
     *
     * @param input 输入事件
     * @return 是否继续处理
     */
    @Override
    public boolean isContinue(EventDO input) {
        return super.isContinue(input) &&
                kEventProperties.getTrexSyncEventName().containsKey(input.getName());
    }
}
