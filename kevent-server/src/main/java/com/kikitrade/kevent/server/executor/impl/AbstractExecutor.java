package com.kikitrade.kevent.server.executor.impl;

import com.alibaba.fastjson.JSONObject;
import com.kikitrade.kevent.common.constant.EventConstants;
import com.kikitrade.kevent.dal.builder.EventStoreBuilder;
import com.kikitrade.kevent.dal.model.EventDO;
import com.kikitrade.kevent.server.autoconfigure.KEventProperties;
import com.kikitrade.kevent.server.executor.Executor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;

/**
 * @author: penuel
 * @date: 2022/5/5 12:18
 * @desc: 默认实现
 */
@Slf4j
public abstract class AbstractExecutor implements Executor {
    @Autowired
    private KEventProperties kEventProperties;
    @Autowired
    private EventStoreBuilder eventStoreBuilder;

    protected String _first = "_first";

    /**
     * 执行逻辑
     *
     * @param input
     * @return
     */

    public EventConstants.DeliveryStatus doExecute(EventDO input) {
        return EventConstants.DeliveryStatus.FAIL;
    }

    /**
     *
     * @param input
     */
    @Async("keventExecutor")
    @Override
    public void execute(EventDO input) {
        log.info("{} process input {}", this.getClass().getName(), JSONObject.toJSONString(input));
        if (eventStoreBuilder.updateStatusByType(input.getId(), deliveryChannel(), EventConstants.DeliveryStatus.PROCESSING)){
            EventConstants.DeliveryStatus deliveryStatus = doExecute(input);
            eventStoreBuilder.updateStatusByType(input.getId(), deliveryChannel(), deliveryStatus);
        }
    }

    @Override
    public boolean isContinue(EventDO input) {
        EventConstants.DeliveryChannel deliveryChannel = deliveryChannel();
        EventConstants.DeliveryStatus deliveryStatus = eventStoreBuilder.getDeliveryStatusByType(input.getId(), deliveryChannel);
        log.info("isContinue process input {}, deliveryChannel {}, deliveryStatus {}", JSONObject.toJSONString(input), deliveryChannel, deliveryStatus);
        if (EventConstants.DeliveryStatus.SUCCESS.equals(deliveryStatus)
                || EventConstants.DeliveryStatus.UNKNOWN.equals(deliveryStatus)) {
            return false;
        }
        return true;
    }

    /**
     * 分发渠道
     *
     * @return
     */
    /**
     * 分发渠道
     *
     * @return
     */
    public abstract EventConstants.DeliveryChannel deliveryChannel();

}
