package com.kikitrade.kevent.server.channel;

import com.alibaba.fastjson.JSON;
import com.kikitrade.kevent.common.constant.EventConstants;
import com.kikitrade.kevent.common.exception.EventException;
import com.kikitrade.kevent.dal.model.EventDO;
import com.kikitrade.kevent.server.autoconfigure.KEventProperties;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2025/7/22 15:30
 * @description: TrexSyncTrackingExecutor单元测试
 */
@ExtendWith(MockitoExtension.class)
class TrexSyncTrackingExecutorTest {

    @Mock
    private KEventProperties kEventProperties;

    @InjectMocks
    private TrexSyncTrackingExecutor trexSyncTrackingExecutor;

    private Map<String, String> trexSyncConfig;
    private Map<String, String> trexSyncEventName;

    @BeforeEach
    void setUp() {
        trexSyncConfig = new HashMap<>();
        trexSyncConfig.put("app_id", "test-app-id");
        trexSyncConfig.put("api_key", "test-api-key");
        trexSyncConfig.put("host", "https://api.trex.com");

        trexSyncEventName = new HashMap<>();
        trexSyncEventName.put("social_action", "trex_social");

        when(kEventProperties.getTrexSync()).thenReturn(trexSyncConfig);
        when(kEventProperties.getTrexSyncEventName()).thenReturn(trexSyncEventName);
    }

    @Test
    void testMapping_WithValidEventBody_ShouldReturnMappedData() throws EventException {
        // Given
        EventDO event = new EventDO();
        Map<String, Object> eventBody = new HashMap<>();
        eventBody.put("customerId", "12345");
        eventBody.put("platform", "twitter");
        eventBody.put("socialId", "social123");
        eventBody.put("socialHandleName", "@testuser");
        eventBody.put("socialProfileImage", "https://example.com/avatar.jpg");
        
        event.setBody(JSON.toJSONString(eventBody));

        // When
        Map<String, Object> result = trexSyncTrackingExecutor.mapping(event);

        // Then
        assertNotNull(result);
        assertEquals("test-app-id", result.get("appId"));
        assertEquals("12345", result.get("customerId"));
        assertEquals("twitter", result.get("platform"));
        assertEquals("social123", result.get("socialId"));
        assertEquals("@testuser", result.get("socialHandleName"));
        assertEquals("https://example.com/avatar.jpg", result.get("socialProfileImage"));
    }

    @Test
    void testMapping_WithEmptyBody_ShouldReturnNull() throws EventException {
        // Given
        EventDO event = new EventDO();
        event.setBody("");

        // When
        Map<String, Object> result = trexSyncTrackingExecutor.mapping(event);

        // Then
        assertNull(result);
    }

    @Test
    void testMapping_WithNullBody_ShouldReturnNull() throws EventException {
        // Given
        EventDO event = new EventDO();
        event.setBody(null);

        // When
        Map<String, Object> result = trexSyncTrackingExecutor.mapping(event);

        // Then
        assertNull(result);
    }

    @Test
    void testEndpoint_ShouldReturnCorrectUrl() {
        // When
        String endpoint = trexSyncTrackingExecutor.endpoint();

        // Then
        assertEquals("https://api.trex.com/api/v1/social/track", endpoint);
    }

    @Test
    void testDeliveryChannel_ShouldReturnTrexSync() {
        // When
        EventConstants.DeliveryChannel channel = trexSyncTrackingExecutor.deliveryChannel();

        // Then
        assertEquals(EventConstants.DeliveryChannel.TREX_SYNC, channel);
    }

    @Test
    void testIsContinue_WithValidEventName_ShouldReturnTrue() {
        // Given
        EventDO event = new EventDO();
        event.setName("social_action");

        // When
        boolean result = trexSyncTrackingExecutor.isContinue(event);

        // Then
        assertTrue(result);
    }

    @Test
    void testIsContinue_WithInvalidEventName_ShouldReturnFalse() {
        // Given
        EventDO event = new EventDO();
        event.setName("invalid_event");

        // When
        boolean result = trexSyncTrackingExecutor.isContinue(event);

        // Then
        assertFalse(result);
    }

    @Test
    void testMapping_WithPartialData_ShouldMapAvailableFields() throws EventException {
        // Given
        EventDO event = new EventDO();
        Map<String, Object> eventBody = new HashMap<>();
        eventBody.put("customerId", "12345");
        eventBody.put("platform", "twitter");
        // Missing socialId, socialHandleName, socialProfileImage
        
        event.setBody(JSON.toJSONString(eventBody));

        // When
        Map<String, Object> result = trexSyncTrackingExecutor.mapping(event);

        // Then
        assertNotNull(result);
        assertEquals("test-app-id", result.get("appId"));
        assertEquals("12345", result.get("customerId"));
        assertEquals("twitter", result.get("platform"));
        assertFalse(result.containsKey("socialId"));
        assertFalse(result.containsKey("socialHandleName"));
        assertFalse(result.containsKey("socialProfileImage"));
    }
}
