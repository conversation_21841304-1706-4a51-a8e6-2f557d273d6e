package com.kikitrade.kevent.server.mq;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.SendResult;
import com.kikitrade.framework.common.util.BeanUtil;
import com.kikitrade.framework.observability.tracing.annotation.TracingSpan;
import com.kikitrade.framework.observability.tracing.constant.TracingBusiness;
import com.kikitrade.framework.ons.OnsMessageListener;
import com.kikitrade.framework.ons.OnsProducer;
import com.kikitrade.framework.ons.OnsProperties;
import com.kikitrade.kevent.common.constant.EventConstants;
import com.kikitrade.kevent.common.model.EventDTO;
import com.kikitrade.kevent.common.util.DateUtil;
import com.kikitrade.kevent.dal.model.EventDO;
import com.kikitrade.kevent.server.autoconfigure.KEventProperties;
import com.kikitrade.kevent.server.service.EventService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * @author: penuel
 * @date: 2022/5/4 15:19
 * @desc: 唯一事件来源
 */
@Slf4j
@Component
public class EventOnsListener implements OnsMessageListener {

    private static final int laterTimesMinutes = 80;

    private static final int spacingInterval = 10;

    @Autowired
    private KEventProperties kEventProperties;
    @Autowired
    private EventService eventService;
    @Autowired
    @Lazy
    private OnsProducer onsProducer;
    @Resource
    private OnsProperties onsProperties;
    @Resource
    @Lazy
    private EventOnsListener eventOnsListener;

    @Override
    public String topic() {
        return kEventProperties.getOnsTopicEvent();
    }


    @Override
    public boolean traffic() {
        return null != onsProperties && onsProperties.isEnableTraffic();
    }

    @Override
    public Action doConsume(Message message, ConsumeContext consumeContext) {
        return eventOnsListener.doProcess(message, consumeContext);
    }
    @TracingSpan(name = "eventOnsListener", business = TracingBusiness.none)
    public Action doProcess(Message message, ConsumeContext consumeContext) {
        log.info("consume message:{}", JSONObject.toJSONString(message));
        String eventMessage = new String(message.getBody());
        try {
            EventDTO event = JSONObject.parseObject(eventMessage, EventDTO.class);
            List<EventDO> events = parseDTO(event, message.getMsgID());

            boolean result = true;
            for (EventDO eventDO : events) {
                boolean process = eventService.process(eventDO);
                result = result && process;
            }
            if (!result) {
                isDelayMsg(message);
            }
            return Action.CommitMessage;
        } catch (Exception e) {
            log.error("consume message failed, eventMessage:{}", eventMessage, e);
            return Action.ReconsumeLater;
        }
    }

    private boolean isDelayMsg(Message message) {

        String eventMessage = new String(message.getBody());
        EventDTO event = JSONObject.parseObject(eventMessage, EventDTO.class);
        // 避免以msgId为主键的记录，成功的渠道依然上报
        if (StringUtils.isBlank(event.getGlobalUid())) {
            event.setGlobalUid(message.getMsgID());
            eventMessage = JSONObject.toJSONString(event);
        }
        int space = DateUtil.compareDateMinuteSpace(new Date(), new Date(event.getTime()), spacingInterval);
        if (space * spacingInterval > laterTimesMinutes) {
            return false;
        }
        // 发送延迟消息 10, 20 ,40 ,80 160,
        Date delayDate = DateUtil.addMinToDate(new Date(), (space + 1) * spacingInterval);
        log.info("consume message pushDelayMsg, eventMessage:{}, delayDate:{}", eventMessage, delayDate);
        SendResult sendResult = onsProducer.send(message.getTopic(), eventMessage, delayDate.getTime());
        return false;
    }

    public List<EventDO> parseDTO(EventDTO eventDTO, String id) {
        List<EventDO> events = new ArrayList<>();
        if (StringUtils.isNotBlank(eventDTO.getGlobalUid())) {
            id = eventDTO.getSource() + "_" + eventDTO.getName() + "_" + eventDTO.getGlobalUid();
        } else {
            id = eventDTO.getSource() + "_" + eventDTO.getName() + "_" + id;
        }
        EventDO event1 = BeanUtil.copyProperties(eventDTO, EventDO::new);
        buildEventBodyParam(eventDTO, event1);
        event1.setId(id);
        event1.setName(eventDTO.getName());
        event1.setCreated(new Date());
        event1.setModified(new Date());
        event1.setUid(eventDTO.getGlobalUid());
        event1.setDeviceToken(eventDTO.getDeviceToken());
        event1.setIp(eventDTO.getIp());
        events.add(event1);
        if (StringUtils.isNotBlank(eventDTO.getFirstUid())) {
            String firstId = eventDTO.getSource() + "_first_" + eventDTO.getName() + "_" + eventDTO.getFirstUid();
            EventDO event2 = BeanUtil.copyProperties(eventDTO, EventDO::new);
            event2.setId(firstId);
            event2.setName(eventDTO.getName() + "_first");
            buildEventBodyParam(eventDTO, event2);
            event2.setCreated(new Date());
            event2.setModified(new Date());
            event2.setUid(eventDTO.getGlobalUid());
            event2.setDeviceToken(eventDTO.getDeviceToken());
            event2.setIp(eventDTO.getIp());
            events.add(event2);
        }
        return events;
    }

    private void buildEventBodyParam(EventDTO eventDTO, EventDO eventDO) {
        JSONObject jsonObject = new JSONObject();

        if (StringUtils.isNotBlank(eventDTO.getCurrency())) {
            jsonObject.put(EventConstants.EventBodyConstant.currency, eventDTO.getCurrency());
        }
        if (eventDTO.getAmount() != null) {
            jsonObject.put(EventConstants.EventBodyConstant.revenue, eventDTO.getAmount());
        }
        if (eventDTO.getBody() != null) {
            jsonObject.put(EventConstants.EventBodyConstant.body, eventDTO.getBody());
        }
        if (jsonObject != null && jsonObject.size() > 0) {
            eventDO.setBody(jsonObject.toJSONString());
        }
    }
}
