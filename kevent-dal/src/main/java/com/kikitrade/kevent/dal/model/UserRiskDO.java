package com.kikitrade.kevent.dal.model;

import com.alicloud.openservices.tablestore.model.search.FieldType;
import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.SearchIndex;
import com.kikitrade.framework.ots.annotations.Table;
import lombok.Data;

import java.io.Serializable;


@Data
@Table(name = "user_risk")
public class UserRiskDO implements Serializable {

    // 多元索引
    public static final String SEARCH_INDEX_USER_RISK = "search_idx_user_risk";

    @SearchIndex(name = SEARCH_INDEX_USER_RISK, column = "customer_id", fieldType = FieldType.KEYWORD)
    @PartitionKey(name = "customer_id", type = PartitionKey.Type.STRING)
    private String customerId;

    /**
     * 订单状态，参考{@link com.kikitrade.kevent.common.constant.UserFraudRiskLevel}
     */
    @SearchIndex(name = SEARCH_INDEX_USER_RISK, column = "level", fieldType = FieldType.KEYWORD)
    @Column(name = "level", type = Column.Type.STRING, isDefined = true)
    private String level;

    @SearchIndex(name = SEARCH_INDEX_USER_RISK, column = "score", fieldType = FieldType.DOUBLE)
    @Column(name = "score", type = Column.Type.DOUBLE, isDefined = true)
    private double score;

    @SearchIndex(name = SEARCH_INDEX_USER_RISK, column = "tags", fieldType = FieldType.KEYWORD)
    @Column(name = "tags", type = Column.Type.STRING, isDefined = true)
    private String tags;

    @SearchIndex(name = SEARCH_INDEX_USER_RISK, column = "block", fieldType = FieldType.BOOLEAN)
    @Column(name = "block", type = Column.Type.BOOLEAN, isDefined = true)
    private boolean block;

    @SearchIndex(name = SEARCH_INDEX_USER_RISK, column = "block_time", fieldType = FieldType.KEYWORD)
    @Column(name = "block_time", type = Column.Type.INTEGER, isDefined = true)
    private Long blockTime;

    @SearchIndex(name = SEARCH_INDEX_USER_RISK, column = "unblock_time", fieldType = FieldType.KEYWORD)
    @Column(name = "unblock_time", type = Column.Type.INTEGER, isDefined = true)
    private Long unblockTime;

    @SearchIndex(name = SEARCH_INDEX_USER_RISK, column = "created", fieldType = FieldType.KEYWORD)
    @Column(name = "created", type = Column.Type.INTEGER, isDefined = true)
    private Long created;

    @SearchIndex(name = SEARCH_INDEX_USER_RISK, column = "modified", fieldType = FieldType.KEYWORD)
    @Column(name = "modified", type = Column.Type.INTEGER, isDefined = true)
    private Long modified;

    @SearchIndex(name = SEARCH_INDEX_USER_RISK, column = "saas_id", fieldType = FieldType.KEYWORD)
    @Column(name = "saas_id", type = Column.Type.STRING, isDefined = true)
    private String saasId;
}

