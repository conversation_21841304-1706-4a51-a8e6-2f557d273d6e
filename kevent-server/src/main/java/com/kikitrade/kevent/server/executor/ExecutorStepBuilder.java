package com.kikitrade.kevent.server.executor;

import com.kikitrade.kevent.server.executor.impl.AbstractExecutor;

import java.util.LinkedHashSet;
import java.util.Set;

/**
 * @author: penuel
 * @date: 2022/5/5 14:35
 * @desc: TODO
 */
public class ExecutorStepBuilder<I, O> {

    private Set<ExecutorListener> executorListeners = new LinkedHashSet<>();
    private Set<AbstractExecutor> executors = new LinkedHashSet<>();

    public ExecutorStepBuilder<I, O> builder() {
        return new ExecutorStepBuilder<>();
    }

    public ExecutorStepBuilder<I, O> listener(ExecutorListener listener) {
        executorListeners.add(listener);
        return this;
    }

    public ExecutorStepBuilder<I, O> next(AbstractExecutor executor) {
        executors.add(executor);
        return this;
    }



}
