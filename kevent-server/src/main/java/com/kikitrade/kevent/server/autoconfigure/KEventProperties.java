package com.kikitrade.kevent.server.autoconfigure;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: penuel
 * @date: 2022/5/4 15:22
 * @desc: TODO
 */
@ConfigurationProperties("kevent")
@Data
public class KEventProperties {

    private String onsTopicEvent;

    private String saasId;

    private String env;

    private boolean templateCacheEnable = true;

    private long templateCacheTtlMs = 300000;

    private boolean adjustFix;
    /**
     * Adjust渠道配置
     */
    private Map<String, String> adjust = new HashMap<>();

    private Map<String, String> adjustEventToken = new HashMap<>();

    /**
     * Facebook渠道配置
     */
    private Map<String, String> facebook = new HashMap<>();

    private Map<String, String> facebookEventName = new HashMap<>();

    /**
     * Aliyun风控渠道配置
     */
    private Map<String, String> aliyunRisk = new HashMap<>();

    private Map<String, String> aliyunRiskEventName = new HashMap<>();

    private Map<String, String> errorRetry = new HashMap<>();

    private Map<String, String> eventTemplate = new HashMap<>();

    /**
     * AppsFlyer渠道配置
     */
    private Map<String, String> appsFlyer = new HashMap<>();

    private Map<String, String> appsFlyerEventToken = new HashMap<>();

    private Map<String, String> appsFlyerEventName = new HashMap<>();

    /**
     * GoogleAnalytics渠道配置
     */
    private Map<String, String> googleAnalytics = new HashMap<>();

    private Map<String, String> googleAnalyticsToken = new HashMap<>();

    private Map<String, String> googleAnalyticsEventName = new HashMap<>();

    /**
     * 活动渠道配置
     */
    private Map<String, String> activityEventName = new HashMap<>();

    /**
     * 活动渠道配置
     */
    private Map<String, String> activityEventCode = new HashMap<>();

    //活动支持的事件， 多个英文都好分隔
    private String kactivityEvent;

    //需要过滤的子event
    private String kactivityFilterEventVerb;

    //允许的子event
    private String kactivityAllowEventVerb;

    //活动需要和并的事件，多个引文逗号分隔
    private String kactivityMergeEvent;

    //活动需要分裂的事件，多个引文逗号分隔
    private String kactivitySplitEvent;

    //活动合并后事件
    private Map<String, String> kactivityMergeEventTarget = new HashMap<>();

    //活动分离后事件，value多个引文逗号分隔
    private Map<String, String> kactivitySplitEventTarget = new HashMap<>();

    //event -> activity事件
    private String activityTopicName;

    /**
     * 用户风险等级，由'低'升高后，钉钉通知群聊
     */
    private String userRiskLevelUpNotifyUrl;

    private String updateRisk = "old";

    // 自己对自己的帖子、回复等进行操作后，是否过滤该事件
    private Boolean kactivityEventSelfSkip = true;

    private String onsTopicEventCall;

    private String waitCalEventSaasId = "monster,zeek";

    /**
     * quests sync配置
     */
    private Map<String, String> questSync = new HashMap<>();

    private Map<String, String> questSyncEventName = new HashMap<>();

    /**
     * trex sync配置
     */
    private Map<String, String> trexSync = new HashMap<>();

    private Map<String, String> trexSyncEventName = new HashMap<>();

    /**
     * 清除event数据，保留最近多少天的数据
     */
    private int clearEventDays = 30;
}
