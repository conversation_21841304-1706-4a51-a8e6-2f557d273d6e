package com.kikitrade.kevent.server.service.impl;

import com.alibaba.fastjson.JSON;
import com.kikitrade.kcustomer.api.model.DingTalkMessageDTO;
import com.kikitrade.kcustomer.api.service.RemoteNotificationService;
import com.kikitrade.kevent.common.constant.UserFraudRiskLevel;
import com.kikitrade.kevent.dal.builder.UserRiskBuilder;
import com.kikitrade.kevent.dal.model.UserRiskDO;
import com.kikitrade.kevent.server.autoconfigure.KEventProperties;
import com.kikitrade.kevent.server.service.UserRiskService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;


@Slf4j
@Component
public class UserRiskServiceImpl implements UserRiskService {

    @DubboReference
    private RemoteNotificationService remoteNotificationService;

    @Resource
    private UserRiskBuilder userRiskBuilder;

    @Resource
    private KEventProperties kEventProperties;

    @Override
    public UserRiskDO get(String customerId) {
        return userRiskBuilder.get(customerId);
    }

    @Override
    public boolean updateRisk(UserRiskDO userRiskDO) {

        UserRiskDO oldUserRiskDO = userRiskBuilder.get(userRiskDO.getCustomerId());
        // 用户的风险等级升级，且最新等级 >= MEDIUM 时，发送钉钉通知
        String oldLevel = oldUserRiskDO != null ? oldUserRiskDO.getLevel() : UserFraudRiskLevel.NONE.name();
        if (UserFraudRiskLevel.fromName(userRiskDO.getLevel()).getCode() >= UserFraudRiskLevel.MEDIUM.getCode()
                && UserFraudRiskLevel.fromName(userRiskDO.getLevel()).getCode() > UserFraudRiskLevel.fromName(oldLevel).getCode()) {
            log.info("updateRisk customerId={}, oldLevel={}, level={}", userRiskDO.getCustomerId(), oldLevel, userRiskDO.getLevel());
            sendRiskLevelUpDingTalk(userRiskDO);
        }

        if (null == oldUserRiskDO) {
            try {
                if (userRiskBuilder.create(userRiskDO)) {
                    return true;
                }
            } catch (Exception e) {
                log.error("updateRisk exception data={}", JSON.toJSONString(userRiskDO), e);
            }
        }

        return userRiskBuilder.updateRiskInfo(userRiskDO);
    }

    private void sendRiskLevelUpDingTalk(UserRiskDO userRiskDO) {
        DingTalkMessageDTO dingTalkMessage = buildDingTalkMessage(userRiskDO);
        try {
            if(dingTalkMessage != null){
                boolean send = remoteNotificationService.send(dingTalkMessage);
                log.info("sendRiskLevelUpDingTalk, userRiskDO:{}, dingTalkMessage: {}, result:{}", userRiskDO, dingTalkMessage, send);
            }
        } catch (Exception e) {
            log.error("sendRiskLevelUpDingTalk failed, userRiskDO:{}", userRiskDO, e);
        }
    }

    private DingTalkMessageDTO buildDingTalkMessage(UserRiskDO userRiskDO) {
        log.info("UserRiskService buildDingTalkMessage: {}", userRiskDO);
        DingTalkMessageDTO dingTalkMessageDTO = new DingTalkMessageDTO();
        String userRiskLevelUpNotifyUrl = kEventProperties.getUserRiskLevelUpNotifyUrl();
        if (StringUtils.isBlank(userRiskLevelUpNotifyUrl)) {
            log.info("buildDingTalkMessage fail, userRiskLevelUpNotifyUrl is null");
            return null;
        }
        dingTalkMessageDTO.setNotifyUrl(userRiskLevelUpNotifyUrl);
        String message = "[%s-%s] \n[Risk Warning] \nUID:[%s] \n风险等级:[%s] \n风险分数:[%s] \n命中策略:[%s]";
        message = String.format(message, userRiskDO.getSaasId(), kEventProperties.getEnv(), userRiskDO.getCustomerId(),
                StringUtils.upperCase(userRiskDO.getLevel()), userRiskDO.getScore(), userRiskDO.getTags());
        dingTalkMessageDTO.setMessage(message);
        return dingTalkMessageDTO;
    }

}
