package com.kikitrade.kevent.server.channel;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.shade.org.apache.commons.codec.binary.Hex;
import com.kikitrade.kcustomer.api.model.DeviceInfoDTO;
import com.kikitrade.kevent.common.constant.EventConstants;
import com.kikitrade.kevent.common.constant.EventConstants.DeliveryStatus;
import com.kikitrade.kevent.common.constant.EventConstants.EventBodyConstant;
import com.kikitrade.kevent.common.constant.EventConstants.FacebookConfig;
import com.kikitrade.kevent.common.constant.EventConstants.FacebookConstant;
import com.kikitrade.kevent.common.exception.EventException;
import com.kikitrade.kevent.dal.builder.EventStoreBuilder;
import com.kikitrade.kevent.dal.model.EventDO;
import com.kikitrade.kevent.server.autoconfigure.KEventProperties;
import com.kikitrade.kevent.server.executor.impl.TrackingExecutor;
import com.kikitrade.kevent.server.util.TwoTuple;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import static com.kikitrade.kevent.common.constant.EventMessageEnum.SYSTEM_PARAMETER_REQUIRED;

/**
 * @author: penuel
 * @date: 2022/5/5 15:00
 * @desc: TODO
 */
@Component("faceBookTrackingExecutor")
@Slf4j
@ConditionalOnProperty(name = {"kevent.facebook.api_url"})
public class FaceBookTrackingExecutor extends TrackingExecutor {

    @Resource
    private KEventProperties kEventProperties;
    @Autowired
    public EventStoreBuilder eventStoreBuilder;
    @Override
    public boolean isContinue(EventDO input) {
        boolean result = super.isContinue(input);
        if (result) {
            Set<String> keySet = kEventProperties.getFacebookEventName().keySet();
            if (keySet.parallelStream().anyMatch(k -> regularCheck(k, parseOriginalName(input.getName()).getFirst()))) {
                return true;
            }
            return false;
        }
        return false;
    }

    @Override
    public Map<String, Object> mapping(EventDO event) throws EventException {
        //查询设备信息
        DeviceInfoDTO deviceInfo = getDeviceInfoByCustomerIdOrDeviceId(event.getCustomerId(),event.getDeviceId());
        if (StringUtils.isBlank(deviceInfo.getDeviceId())) {
            throw new EventException(SYSTEM_PARAMETER_REQUIRED);
        }
        Map<String, Object> postParameters = new HashMap<>();
        postParameters.put(FacebookConstant.event, "CUSTOM_APP_EVENTS");
        postParameters.put(FacebookConstant.advertiserId, getOriginalDeviceId(deviceInfo.getDeviceId()));
        //用户启用广告追踪
        postParameters.put(FacebookConstant.advertiserTrackingEnabled, "1");
        //应用级别启用广告追踪
        postParameters.put(FacebookConstant.applicationTrackingEnabled, "1");

        JSONObject jsonObject = new JSONObject();
        jsonObject.put(FacebookConstant.logTime, event.getTime());

        String eventName = getDefinedEventName(event.getName());
        jsonObject.put(FacebookConstant.eventName, eventName);

        JSONObject eventBody = JSONObject.parseObject(event.getBody());
        jsonParamCheckAndSet(jsonObject, eventBody, FacebookConstant.fbDescription, EventBodyConstant.description);
        //Facebook/电子邮箱/Twitter
        jsonObject.put(FacebookConstant.fbRegistrationMethod, "s2s");
        jsonParamCheckAndSet(jsonObject, eventBody, FacebookConstant.fbContentId, EventBodyConstant.contentId);
        /**
         *JSON 对象列表，包含国际商品编码 (EAN)（如适用）或其他商品或内容标识符，以及商品的数量和价格。必须提供：id、quantity。例如，"[{\"id\": \"1234\", \"quantity\": 2,}, {\"id\": \"5678\", \"quantity\": 1,}]"。
         */
        jsonParamCheckAndSet(jsonObject, eventBody, FacebookConstant.fbContent, EventBodyConstant.content);
        //product 或 product_group
        jsonObject.put(FacebookConstant.fbContentType, "product");
        //商品数量
        jsonParamCheckAndSet(jsonObject, eventBody, FacebookConstant.fbNumItems, EventBodyConstant.numItems);

        //如果是入金，将对应金额转为法币
        if (Objects.nonNull(eventBody)
                && Objects.nonNull(eventBody.get(EventBodyConstant.revenue))
                && Objects.nonNull(eventBody.get(EventBodyConstant.currency))) {
            String revenue = String.valueOf(eventBody.get(EventBodyConstant.revenue));
            String currency = String.valueOf(eventBody.get(EventBodyConstant.currency));

            String fiatRevenue = calFiatCurrency(revenue, currency);
            //收入参数
            jsonObject.put(FacebookConstant.fbCurrency, fiatCurrency);
            jsonObject.put(FacebookConstant._valueToSum, fiatRevenue);

            JSONObject jo = new JSONObject();
            jo.put(EventBodyConstant.currency, currency);
            jo.put(EventBodyConstant.revenue, revenue);
            jsonObject.put(FacebookConstant.fbDescription, jo.toJSONString());
        }

        postParameters.put(FacebookConstant.customEvents, Lists.newArrayList(jsonObject.toJSONString()));

        String appAccessToken = getFacebookConfig().get(FacebookConfig.appAccessToken);
        postParameters.put(FacebookConstant.appAccessToken, appAccessToken);
        postParameters.put(FacebookConstant.ud_externalId, getSHA256Str(event.getUid()));
        return postParameters;
    }

    @Override
    public EventConstants.DeliveryStatus track(Map<String, Object> params) {
        try {
            return apiCall(HttpMethod.POST, EventConstants.DeliveryStatus.class, params, null);
        } catch (Exception e) {
            log.warn("FaceBook track data error", e);
            return DeliveryStatus.FAIL;
        }
    }


    @Override
    public String endpoint() {
        String appId = getFacebookConfig().get(FacebookConfig.appId);
        String eventUrl = getFacebookConfig().get(FacebookConfig.apiUrl);
        return eventUrl.replace("{app-id}", appId);
    }

    @Override
    public EventConstants.DeliveryChannel deliveryChannel() {
        return EventConstants.DeliveryChannel.FACEBOOK;
    }

    Map<String, String> getFacebookConfig() {
        return kEventProperties.getFacebook();
    }

    Map<String, String> getFacebookEventName() {
        return kEventProperties.getFacebookEventName();
    }

    /***
     *SHA-256加密
     */
    public static String getSHA256Str(String str) {
        if (StringUtils.isBlank(str)){
            return str;
        }
        MessageDigest messageDigest;
        String encdeStr = "";
        try {
            messageDigest = MessageDigest.getInstance("SHA-256");
            byte[] hash = messageDigest.digest(str.getBytes("UTF-8"));
            encdeStr = Hex.encodeHexString(hash);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return encdeStr;
    }


    /**
     * 自定义事件名映射
     *
     * @param eventName
     * @return
     */
    protected String getDefinedEventName(String eventName) {
        TwoTuple<String, String> twoTuple = parseOriginalName(eventName);
        //原始事件名称
        String preName = twoTuple.getFirst();
        //后缀
        String suffix = twoTuple.getSecond();
        String definedEventName = getFacebookEventName().get("__" + preName + "__" + suffix);
        if (StringUtils.isNotBlank(definedEventName)) {
            eventName = definedEventName;
        } else {
            // default mapping
            if (!eventName.startsWith("s2s")) {
                eventName = "s2s_" + eventName;
            }
        }
        return eventName;
    }
}
