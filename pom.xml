<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>
    <parent>
        <groupId>com.kikitrade</groupId>
        <artifactId>kiki-framework</artifactId>
        <version>2.5.0-SNAPSHOT</version>
    </parent>

    <artifactId>kevent</artifactId>
    <version>2.0.2-SNAPSHOT</version>
    <name>kevent</name>
    <description>Event Collector and Dispatcher for KKTD</description>
    <properties>
        <kcustomer.version>2.0.3-SNAPSHOT</kcustomer.version>
        <kmarket.version>2.0.0-SNAPSHOT</kmarket.version>
        <kcompute.version>2.0.0-SNAPSHOT</kcompute.version>
    </properties>

    <modules>
        <module>kevent-common</module>
        <module>kevent-dal</module>
        <module>kevent-client</module>
        <module>kevent-server</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.kikitrade</groupId>
                <artifactId>kevent-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.kikitrade</groupId>
                <artifactId>kevent-dal</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.kikitrade</groupId>
                <artifactId>kevent-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.kikitrade</groupId>
                <artifactId>kevent-server</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.kikitrade</groupId>
                <artifactId>kcustomer-api</artifactId>
                <version>${kcustomer.version}</version>
            </dependency>
            <dependency>
                <groupId>com.kikitrade</groupId>
                <artifactId>kmarket-client</artifactId>
                <version>${kmarket.version}</version>
            </dependency>
            <dependency>
                <groupId>com.kikitrade</groupId>
                <artifactId>kmarket-api</artifactId>
                <version>${kmarket.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-compiler-plugin</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>

        <dependency>
            <groupId>com.kikitrade</groupId>
            <artifactId>kcustomer-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
            <version>2.2.0.RELEASE</version>
        </dependency>

    </dependencies>

    <profiles>
        <profile>
            <id>local</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <project.profile.id>local</project.profile.id>
            </properties>
            <build>
                <filters>
                    <filter>${project.property.path}/config-local.properties</filter>
                </filters>
            </build>
            <repositories>
                <repository>
                    <id>snapshot</id>
                    <url>https://nexus.dipbit.xyz/repository/maven-snapshots/</url>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                    <releases>
                        <enabled>false</enabled>
                    </releases>
                </repository>

                <repository>
                    <id>public</id>
                    <!--<url>https://nexus.dipbit.xyz/nexus/groups/public/</url>-->
                    <url>https://nexus.dipbit.xyz/repository/maven-public/</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </repository>

                <repository>
                    <id>releases</id>
                    <url>https://nexus.dipbit.xyz/repository/maven-releases/</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </repository>

                <repository>
                    <id>thirdparty</id>
                    <url>https://nexus.dipbit.xyz/repository/thirdparty</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </repository>

                <repository>
                    <id>bintray.com</id>
                    <!--<url>https://dl.bintray.com/spark-packages/maven/</url>-->
                    <url>https://nexus.dipbit.xyz/repository/bintray.com/</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </repository>
                <repository>
                    <id>maven-public</id>
                    <url>https://nexus.dipbit.xyz/repository/maven-public/</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </repository>
                <repository>
                    <id>jahia.org</id>
                    <!-- <url>http://maven.jahia.org/maven2</url>-->
                    <url>https://nexus.dipbit.xyz/repository/jahia.org/</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </repository>

                <repository>
                    <id>clojars.org</id>
                    <url>https://nexus.dipbit.xyz/repository/clojars.org/</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                    <!--<url>http://clojars.org/repo/</url>-->
                </repository>

                <repository>
                    <id>cloudera</id>
                    <!--<url>https://repository.cloudera.com/artifactory/cloudera-repos/</url>-->
                    <url>https://nexus.dipbit.xyz/repository/cloudera/</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </repository>

                <repository>
                    <id>spring-jcenter-cache</id>
                    <!--<url>http://repo.spring.io/jcenter-cache/</url>-->
                    <url>https://nexus.dipbit.xyz/repository/spring-jcenter-cache</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </repository>

                <repository>
                    <id>repo.maven.apache.org</id>
                    <!--<url>https://dl.bintray.com/spark-packages/maven/</url>-->
                    <url>https://nexus.dipbit.xyz/repository/repo.maven.apache.org</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </repository>
                <repository>
                    <id>spark-thirdpart</id>
                    <!--<url>https://dl.bintray.com/spark-packages/maven/</url>-->
                    <url>https://nexus.dipbit.xyz/repository/spark-thirdpart</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </repository>
                <repository>
                    <id>jitpack.io</id>
                    <url>https://jitpack.io</url>
                </repository>
                <repository>
                    <id>sonatype-nexus-staging</id>
                    <name>Sonatype Nexus Staging</name>
                    <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
            </repositories>
            <distributionManagement>
                <repository>
                    <id>releases</id>
                    <name>Local Nexus Repository</name>
                    <url>https://nexus.dipbit.xyz/repository/maven-releases/</url>
                </repository>
                <snapshotRepository>
                    <id>snapshot</id>
                    <name>Local Nexus Repository</name>
                    <url>https://nexus.dipbit.xyz/repository/maven-snapshots/</url>
                </snapshotRepository>
            </distributionManagement>
            <pluginRepositories>
                <pluginRepository>
                    <id>maven-public</id>
                    <url>https://nexus.dipbit.xyz/repository/maven-public/</url>
                </pluginRepository>
                <pluginRepository>
                    <id>repo.maven.apache.org</id>
                    <url>https://nexus.dipbit.xyz/repository/repo.maven.apache.org</url>
                </pluginRepository>
            </pluginRepositories>
        </profile>
    </profiles>


    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <filtering>false</filtering>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
