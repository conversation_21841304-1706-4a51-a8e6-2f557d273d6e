//package com.kikitrade.kevent.server.autoconfigure;
//
//import com.aliyun.credentials.AlibabaCloudCredentials;
//import com.aliyun.credentials.provider.DefaultCredentialsProvider;
//import com.aliyun.credentials.utils.AuthUtils;
//import com.aliyun.credentials.utils.StringUtils;
//import com.aliyuncs.DefaultAcsClient;
//import com.aliyuncs.IAcsClient;
//import com.aliyuncs.auth.AlibabaCloudCredentialsProvider;
//import com.aliyuncs.auth.BasicCredentials;
//import com.aliyuncs.auth.ECSMetadataServiceCredentialsFetcher;
//import com.aliyuncs.auth.InstanceProfileCredentials;
//import com.aliyuncs.exceptions.ClientException;
//import com.aliyuncs.profile.DefaultProfile;
//import com.kikitrade.kevent.common.constant.EventConstants;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
//import org.springframework.boot.context.properties.EnableConfigurationProperties;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
//import java.util.Map;
//
///**
// * CredentialConfiguration -- only load when aliyun risk adapter in
// *
// * <AUTHOR>
// * @create 2022/6/28 11:05 下午
// * @modify
// */
//@Slf4j
//@Configuration
//@EnableConfigurationProperties(KEventProperties.class)
//@ConditionalOnProperty(name = {"kevent.aliyun-risk.domain"})
//public class CredentialConfiguration {
//
//    @Bean
//    public IAcsClient iAcsClient(KEventProperties kEventProperties) {
//        return getDefaultAcsClient(kEventProperties);
//    }
//
//    public IAcsClient getDefaultAcsClient(KEventProperties kEventProperties) {
//        Map<String, String> aliyunRisk = kEventProperties.getAliyunRisk();
//        String region = aliyunRisk.get(EventConstants.AliyunRiskConfig.region);
//        String ak = aliyunRisk.get(EventConstants.AliyunRiskConfig.ak);
//        String sk = aliyunRisk.get(EventConstants.AliyunRiskConfig.sk);
//
//        DefaultProfile profile = DefaultProfile.getProfile("ap-southeast 1", ak, sk);
//        IAcsClient client = new DefaultAcsClient(profile);
//        return client;
//    }
//
//
//    public IAcsClient getDefaultAcsClient() {
//        DefaultProfile profile = DefaultProfile.getProfile();
//        DefaultAcsClient acsClient = null;
//        String ramRole = AuthUtils.getEnvironmentECSMetaData();
//
//        if (StringUtils.isEmpty(ramRole)) {
//            AlibabaCloudCredentials credentials = (new DefaultCredentialsProvider()).getCredentials();
//            BasicCredentials baseCredentials = new BasicCredentials(credentials.getAccessKeyId(), credentials.getAccessKeySecret());
//            acsClient = new DefaultAcsClient(profile, baseCredentials);
//            log.info("getDefaultAcsClient key:{}, secret:{}",
//                    null == baseCredentials.getAccessKeyId() ? "null" : (baseCredentials.getAccessKeyId().substring(0, 3) + "***"),
//                    null == baseCredentials.getAccessKeySecret() ? "null" : (baseCredentials.getAccessKeySecret().substring(0, 3) + "***"));
//        } else {
//            CustomInstanceProfileCredentialsProvider provider = new CustomInstanceProfileCredentialsProvider(ramRole);
//            acsClient = new DefaultAcsClient(profile, provider);
//            log.info("getDefaultAcsClient role:{}", ramRole);
//        }
//        return acsClient;
//    }
//
//    public class CustomInstanceProfileCredentialsProvider implements AlibabaCloudCredentialsProvider {
//
//        private InstanceProfileCredentials credentials = null;
//        public int ecsMetadataServiceFetchCount = 0;
//        private ECSMetadataServiceCredentialsFetcher fetcher;
//        private static final int MAX_ECS_METADATA_FETCH_RETRY_TIMES = 3;
//        private int maxRetryTimes = MAX_ECS_METADATA_FETCH_RETRY_TIMES;
//        private final String roleName;
//
//        public CustomInstanceProfileCredentialsProvider(String roleName) {
//            if (null == roleName) {
//                throw new NullPointerException("You must specifiy a valid role name.");
//            }
//            this.roleName = roleName;
//            this.fetcher = new ECSMetadataServiceCredentialsFetcher();
//            this.fetcher.setRoleName(this.roleName);
//        }
//
//        @Override
//        public com.aliyuncs.auth.AlibabaCloudCredentials getCredentials() throws ClientException {
//            if (credentials == null) {
//                ecsMetadataServiceFetchCount += 1;
//                credentials = fetcher.fetch(maxRetryTimes);
//            } else if (credentials.isExpired()) {
//                credentials = fetcher.fetch(maxRetryTimes);
//                log.warn("SDK.SessionTokenExpired", "Current session token has expired.");
//            } else if (credentials.willSoonExpire() && credentials.shouldRefresh()) {
//                try {
//                    ecsMetadataServiceFetchCount += 1;
//                    credentials = fetcher.fetch();
//                } catch (ClientException e) {
//                    // Use the current expiring session token and wait for next round
//                    credentials.setLastFailedRefreshTime();
//                }
//            }
//            return credentials;
//        }
//    }
//}
