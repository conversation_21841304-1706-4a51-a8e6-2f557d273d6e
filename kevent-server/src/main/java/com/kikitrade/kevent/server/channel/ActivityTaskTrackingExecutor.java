package com.kikitrade.kevent.server.channel;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.SendResult;
import com.kikitrade.framework.ons.OnsProducer;
import com.kikitrade.kevent.common.constant.CharSequenceConstants;
import com.kikitrade.kevent.common.constant.EventConstants;
import com.kikitrade.kevent.common.exception.EventException;
import com.kikitrade.kevent.common.model.ActivityEventMessage;
import com.kikitrade.kevent.dal.model.EventDO;
import com.kikitrade.kevent.server.autoconfigure.KEventProperties;
import com.kikitrade.kevent.server.executor.impl.TrackingExecutor;
import com.kikitrade.kevent.server.nacos.TaskCodeConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component("activityTaskTrackingExecutor")
@ConditionalOnProperty(name = "kevent.activity-topic-name")
public class ActivityTaskTrackingExecutor extends TrackingExecutor {

    @Resource
    private KEventProperties kEventProperties;
    @Autowired
    @Lazy
    private OnsProducer onsProducer ;

    private static final String EVENT_CODE = "eventCode";
    private static final String CUSTOMER_ID = "customerId";
    private static final String EVENT_TIME = "eventTime";
    private static final String GLOBAL_UID = "globalUid";
    private static final String TARGET_ID = "targetId";
    private static final String VERB = "verb";
    private static final String BODY = "body";
    private static final String ORDER_STATUS = "orderStatus";
    private static final String ORDER_ID = "orderId";
    private static final String TARGET_CUSTOMER_ID = "targetCustomerId";
    private static final String VIP_LEVEL = "vipLevel";
    private static final String TARGET_VIP_LEVEL = "targetVipLevel";
    private static final String CALL_EVENT = "callEvent";
    private static final String ASSA_ID = "saasId";
    private static final String SOURCE_CUSTOMER_ID = "sourceCustomerId";
    private static final String TARGET_CONTENT_ID = "targetContentId";
    private static final String INCR_PROGRESS = "_incr_progress";
    private static final String SOCIAL_ID = "socialId";
    private static final String SOCIAL_NAME = "socialName";

    /**
     * 映射事件名称
     *
     * @param event
     */
    @Override
    public Map<String, Object> mapping(EventDO event) throws EventException {
        Map<String, Object> map = new HashMap<>();
        map.put(EVENT_CODE, event.getName());
        map.put(CUSTOMER_ID, event.getCustomerId());
        map.put(EVENT_TIME, event.getTime());
        map.put(GLOBAL_UID, event.getUid());
        map.put(TARGET_ID, event.getUid());
        map.put("event_message", JSON.toJSONString(event));
        map.put(CALL_EVENT, event.getCallEvent());

        if(StringUtils.isNotBlank(event.getBody())){
            JSONObject body = JSONObject.parseObject(event.getBody());
            if(StringUtils.isNotBlank(body.getString(BODY))){
                JSONObject eventBody = JSONObject.parseObject(body.getString(BODY));
//                map.put(TARGET_ID, eventBody.getString(TARGET_ID) == null ? event.getUid() : eventBody.getString(TARGET_ID));
                map.put(SOCIAL_ID, eventBody.getString(SOCIAL_ID));
                map.put(SOCIAL_NAME, eventBody.getString(SOCIAL_NAME));
                map.put(VERB, eventBody.getString(VERB));
                map.put(SOURCE_CUSTOMER_ID, eventBody.getString(SOURCE_CUSTOMER_ID));
                map.put(INCR_PROGRESS, eventBody.getString(INCR_PROGRESS));
                if(EventConstants.EventName.ORDER_CANCELLED.getName().equalsIgnoreCase(event.getName())
                        || EventConstants.EventName.ORDER_CREATED.getName().equalsIgnoreCase(event.getName())
                        || EventConstants.EventName.ORDER_MATCHED.getName().equalsIgnoreCase(event.getName())){
                    map.put(TARGET_ID, eventBody.getString(ORDER_ID));
                    map.put(VERB, eventBody.getString(ORDER_STATUS));
                }
                TaskCodeConfig taskCodeConfig = TaskCodeConfig.getValue(event.getName());
                if (Objects.nonNull(taskCodeConfig) && StringUtils.isNotBlank(taskCodeConfig.getSplitCode())) {
                    if(eventBody.containsKey(TARGET_CUSTOMER_ID)){
                        map.put(TARGET_CUSTOMER_ID, eventBody.getString(TARGET_CUSTOMER_ID));
                        map.put(TARGET_CONTENT_ID, eventBody.getString(TARGET_ID) == null ? event.getUid() : eventBody.getString(TARGET_ID));
                    }
                }
            }
        }
        log.info("ActivityTaskTrackingExecutor mapping:{}", JSON.toJSONString(map));
        return map;
    }

    /**
     * 上报数据
     *
     * @param params
     * @return
     */
    @Override
    public EventConstants.DeliveryStatus track(Map<String, Object> params) {
        String srcEventCode = String.valueOf(params.get(EVENT_CODE));
        TaskCodeConfig taskCodeConfig = TaskCodeConfig.getValue(srcEventCode);
        String srcEventCodeVerb = null;
        TaskCodeConfig taskCodeConfigVerb = null;
        if (params.get(VERB) != null && StringUtils.isNotBlank(String.valueOf(params.get(VERB)))) {
            srcEventCodeVerb = String.format("%s-%s", srcEventCode, String.valueOf(params.get(VERB)));
            taskCodeConfigVerb = TaskCodeConfig.getValue(srcEventCodeVerb);
        }
        ActivityEventMessage build = ActivityEventMessage.builder()
                .customerId(String.valueOf(params.get(CUSTOMER_ID)))
                .eventCode(srcEventCode)
                .eventTime(Long.parseLong(String.valueOf(params.get(EVENT_TIME))))
                .globalUid(String.valueOf(params.get(GLOBAL_UID)))
                .targetId(String.valueOf(params.get(TARGET_ID)))
                .targetContentId(String.valueOf(params.get(TARGET_CONTENT_ID)))
                .verb(params.get(VERB) == null ? null : String.valueOf(params.get(VERB)))
                .message(String.valueOf(params.get("event_message")))
                .targetCustomerId(String.valueOf(params.getOrDefault(TARGET_CUSTOMER_ID, "")))
                .sourceCustomerId(String.valueOf(params.getOrDefault(SOURCE_CUSTOMER_ID, "")))
                .callEvent(params.get(CALL_EVENT) == null ? null : String.valueOf(params.get(CALL_EVENT)))
                .inc(params.get(INCR_PROGRESS) == null ? null : Integer.valueOf((String)params.get(INCR_PROGRESS)))
                .build();
        if(StringUtils.isNotBlank(kEventProperties.getKactivityMergeEvent()) && (Arrays.stream(kEventProperties.getKactivityMergeEvent().split(CharSequenceConstants.COMMA)).anyMatch(t -> t.equalsIgnoreCase(srcEventCode)))){
            return mergeTrack(build);
        }else if(StringUtils.isNotBlank(build.getVerb()) && StringUtils.isNotBlank(kEventProperties.getKactivityMergeEvent()) && (Arrays.stream(kEventProperties.getKactivityMergeEvent().split(CharSequenceConstants.COMMA)).anyMatch(t -> t.equalsIgnoreCase(String.format("%s-%s", srcEventCode, build.getVerb()))))){
            build.setEventCode(String.format("%s-%s", srcEventCode, build.getVerb()).replace("_", "-"));
            return mergeTrack(build);
        }else if(Objects.nonNull(taskCodeConfig) && StringUtils.isNotBlank(taskCodeConfig.getSplitCode())){
            return splitTrack(build, taskCodeConfig);
        }else if(Objects.nonNull(taskCodeConfigVerb) && StringUtils.isNotBlank(taskCodeConfigVerb.getSplitCode())){
            build.setEventCode(srcEventCodeVerb);
            return splitTrack(build, taskCodeConfigVerb);
        }else{
            return track(build);
        }
    }

    @Override
    public String endpoint() {
        return kEventProperties.getActivityTopicName();
    }

    /**
     * 分发渠道
     *
     * @return
     */
    @Override
    public EventConstants.DeliveryChannel deliveryChannel() {
        return EventConstants.DeliveryChannel.ACTIVITY;
    }

    @Override
    public boolean isContinue(EventDO input) {
        boolean result = super.isContinue(input);
        if(!result){
            return false;
        }
        log.info("ActivityTaskTrackingExecutor isContinue:{}", JSON.toJSONString(input));
        TaskCodeConfig taskCodeConfig = TaskCodeConfig.getValue(input.getName());
        if(taskCodeConfig == null){
            log.info("ActivityTaskTrackingExecutor task not found:{}", input.getName());
            return false;
        }
        JSONObject body = JSONObject.parseObject(input.getBody());
        if(body == null){
            return true;
        }
        if(StringUtils.isBlank(body.getString(BODY))){
            return true;
        }
        JSONObject eventBody = JSONObject.parseObject(body.getString(BODY));
        String verb = eventBody.getString(VERB);
        if(EventConstants.EventName.ORDER_CANCELLED.getName().equalsIgnoreCase(input.getName())
                || EventConstants.EventName.ORDER_CREATED.getName().equalsIgnoreCase(input.getName())
                || EventConstants.EventName.ORDER_MATCHED.getName().equalsIgnoreCase(input.getName())){
            verb = eventBody.getString(ORDER_STATUS);
        }
        if(verb != null){
            String code_verb = String.format("%s_%s", input.getName(), verb);
            boolean filter = Arrays.stream(kEventProperties.getKactivityFilterEventVerb().split(CharSequenceConstants.COMMA)).anyMatch(t -> t.equalsIgnoreCase(code_verb));
            //如果在filter中，不进行
            if(filter){
                return false;
            }
            //如果在allow中，
            if(StringUtils.isNotBlank(kEventProperties.getKactivityAllowEventVerb()) && Arrays.stream(kEventProperties.getKactivityAllowEventVerb().split(CharSequenceConstants.COMMA)).anyMatch(t -> t.startsWith(input.getName()))){
                return Arrays.stream(kEventProperties.getKactivityAllowEventVerb().split(CharSequenceConstants.COMMA)).anyMatch(t -> t.equalsIgnoreCase(code_verb));
            }
            return true;
        }
        return true;
    }

    /**
     * code合并发送
     * @param build
     * @return
     */
    private EventConstants.DeliveryStatus mergeTrack(ActivityEventMessage build){
        try{
            log.info("ActivityTaskTrackingExecutor mergeTrack:{}", build);
            String code = kEventProperties.getKactivityMergeEventTarget().get(build.getEventCode());
            if(StringUtils.isBlank(code)){
                return EventConstants.DeliveryStatus.SUCCESS;
            }
            build.setEventCode(code);
            // 自己对自己的帖子分享
            if (selfSkip(build.getEventCode(), build.getCustomerId(), build.getTargetCustomerId())) {
                return EventConstants.DeliveryStatus.SUCCESS;
            }
            send(JSON.toJSONString(build));
            return EventConstants.DeliveryStatus.SUCCESS;
        }catch (Exception ex){
            log.error("ActivityTaskTrackingExecutor exception{}", build, ex);
            return EventConstants.DeliveryStatus.FAIL;
        }
    }

    /**
     * code合并发送
     * @param build
     * @return
     */
    private EventConstants.DeliveryStatus splitTrack(ActivityEventMessage build, TaskCodeConfig taskCodeConfig){
        try{
            if(build.getCustomerId() == null || build.getTargetCustomerId() == null){
                return EventConstants.DeliveryStatus.SUCCESS;
            }
            JSONObject mjsonObject = JSON.parseObject(build.getMessage());
            JSONObject mbody = mjsonObject.getJSONObject(BODY);
            JSONObject minBody = mbody.getJSONObject(BODY);
            log.info("ActivityTaskTrackingExecutor minbody:{}", mbody);
            if(build.getCallEvent() == null && EventConstants.EventName.REGISTRATION.getName().equals(build.getEventCode()) && Arrays.asList(kEventProperties.getWaitCalEventSaasId().split(",")).contains(minBody.get(ASSA_ID))){
                return EventConstants.DeliveryStatus.WAIT_CALL;
            }
            log.info("ActivityTaskTrackingExecutor splitTrack:{}", build);
            String code = taskCodeConfig.getSplitCode();
            String[] codeArr = code.split(CharSequenceConstants.COMMA);
            for(String c : codeArr){
                build.setEventCode(c);
                if (selfSkip(build.getEventCode(), build.getCustomerId(), build.getTargetCustomerId())) {
                    continue;
                }
                // _结尾的事件为关联事件
                if(c.endsWith("_")){
                    build.setEventCode(c.substring(0, c.length() - 1));
                    build.setSourceCustomerId(build.getCustomerId());
                    if (StringUtils.isBlank(build.getTargetCustomerId())) {
                        continue;
                    } else {
                        build.setCustomerId(build.getTargetCustomerId());
                    }
                    build.setTargetId(build.getTargetContentId());
                    if(build.getMessage() != null){
                        JSONObject jsonObject = JSON.parseObject(build.getMessage());
                        JSONObject body = jsonObject.getJSONObject(BODY);
                        JSONObject inBody = body.getJSONObject(BODY);
                        inBody.put(VIP_LEVEL, inBody.getString(TARGET_VIP_LEVEL));
                        body.put(BODY, JSON.toJSONString(inBody));
                        JSONObject messageBody = new JSONObject();
                        messageBody.put(BODY, JSON.toJSONString(body));
                        build.setMessage(JSON.toJSONString(messageBody));
                    }
                }
                send(JSON.toJSONString(build));
            }
            return EventConstants.DeliveryStatus.SUCCESS;
        }catch (Exception ex){
            log.error("ActivityTaskTrackingExecutor exception{}", build, ex);
            return EventConstants.DeliveryStatus.FAIL;
        }
    }

    /**
     * code合并发送
     * @param build
     * @return
     */
    private EventConstants.DeliveryStatus track(ActivityEventMessage build){
        try{
            log.info("ActivityTaskTrackingExecutor track:{}", build);
            // 自己给自己的帖子评论、自己给自己的评论回复，过滤掉
            if (selfSkip(build.getEventCode(), build.getCustomerId(), build.getTargetCustomerId())) {
                return EventConstants.DeliveryStatus.SUCCESS;
            }
            send(JSON.toJSONString(build));
            return EventConstants.DeliveryStatus.SUCCESS;
        }catch (Exception ex){
            log.error("ActivityTaskTrackingExecutor exception{}", build, ex);
            return EventConstants.DeliveryStatus.FAIL;
        }
    }

    private boolean selfSkip(String eventCode, String customerId, String targetCustomerId) {
        if (BooleanUtils.isFalse(kEventProperties.getKactivityEventSelfSkip())) {
            return false;
        }
        if (!EventConstants.EventName.selfSkipEventNames().contains(eventCode)) {
            return false;
        }
        if (targetCustomerId != null && targetCustomerId.equals(customerId)) {
            log.info("ActivityTaskTrackingExecutor selfSkip, eventCode: {}, customerId: {}", eventCode, customerId);
            return true;
        }
        return false;
    }

    private void send(String message){
        SendResult send = onsProducer.send(endpoint(), message);
        log.info("ActivityTaskTrackingExecutor send messageId:{},message:{}", send.getMessageId(), message);
    }
}
