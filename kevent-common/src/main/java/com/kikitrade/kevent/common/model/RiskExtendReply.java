package com.kikitrade.kevent.common.model;

import lombok.Data;

import java.io.Serializable;

/**
 * @author: wang
 * @date: 2023/3/20
 * @desc: 风控额外结果
 */
@Data
public class RiskExtendReply implements Serializable {
    /**
     * 具体的用户ID/ip/deviceId/email/phone
     * block or pending的对象
     */
    String target;
    /**
     * block or pending的类型
     * USER, IP, DEVICE, EMAIL, PHONE
     */
    String targetType;
    /**
     * 决策结果
     * REJECT, PENDING
     */
    String result;
    /**
     * 受影响的业务
     * WITHDRAW, RED_POCKET, POST, COMMENTS, LOGIN, ALL
     */
    String business;
    /**
     * 业务block时间（单位：天）
     */
    String period;
    /**
     * 业务pending时的验证项
     * GOOGLE_VERIFY, REGISTER_VERIFY, FULL_VERIFY, GOOGLE_RECAPTCHA_VERIFY
     */
    String nextStep;

    String deviceToken;
    /**
     * 设备
     */
    String env;

}
