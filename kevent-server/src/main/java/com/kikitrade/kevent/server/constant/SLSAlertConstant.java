package com.kikitrade.kevent.server.constant;

import lombok.Getter;

public class SLSAlertConstant {
    /**
     * 日志告警
     * error格式: alarm message, [告警标题] 明细
     */
    @Getter

    public enum AlarmSceneEnum{
        /**
         * 消息上报、解析失败
         */
        reportFail("alarmMessage, [ReportFail channel:{}] "),
        paramCheckFail("alarmMessage, [paramCheckFail channel:{}]"),
        // todo  其他类型的报错
        ;
        private String error;

        AlarmSceneEnum(String error){
            this.error = error;
        }
    }
    /**
     * 大盘监控
     */
    @Getter
    public enum DataMonitorEnum{
        report("monitoring, [report,channel:{}]"),
        reportSuccess("monitoring, [reportSuccess,channel:{}]"),
        // TODO: 2022/7/1  其他类型的报错
        ;
        private String monitor;
        DataMonitorEnum(String monitor){
            this.monitor = monitor;
        }
    }
}
