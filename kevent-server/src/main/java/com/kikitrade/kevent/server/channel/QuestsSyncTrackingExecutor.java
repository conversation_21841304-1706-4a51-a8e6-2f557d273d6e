package com.kikitrade.kevent.server.channel;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.esotericsoftware.kryo.util.ObjectMap;
import com.kikitrade.kevent.common.constant.EventConstants;
import com.kikitrade.kevent.common.exception.EventException;
import com.kikitrade.kevent.common.util.SignatureUtil;
import com.kikitrade.kevent.dal.model.EventDO;
import com.kikitrade.kevent.server.autoconfigure.KEventProperties;
import com.kikitrade.kevent.server.executor.impl.TrackingExecutor;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

@Slf4j
@Component("questsSyncTrackingExecutor")
@ConditionalOnProperty(name = "kevent.activity-topic-name")
public class QuestsSyncTrackingExecutor extends TrackingExecutor {

    @Resource
    private KEventProperties kEventProperties;

    @Override
    public TreeMap<String, Object> mapping(EventDO event) throws EventException {
        log.info("QuestsSyncTrackingExecutor mapping event {}", event);
        String body = event.getBody();
        if(StringUtils.isBlank(body)){
            return null;
        }
        JSONObject object = JSON.parseObject(body);
        JSONObject bodyObject = JSON.parseObject(object.getString("body"));
        //object : {"customerId":"2024090816182431551255","event":"UPGRADED","expireTime":1733380222734,"nextLevel":"Lv42","oldLevel":"Lv41","orderId":"2024120606302273943176","saasId":"mugen","timestamp":1733466622794}
        Map<String, String> progressInfo = new HashMap<>();
        progressInfo.put("desc", bodyObject.getString("desc"));
        progressInfo.put("level", bodyObject.getString("nextLevel").replaceAll("[^0-9]", ""));

        String oldLevel = bodyObject.getString("oldLevel").replaceAll("[^0-9]", "");
        String nextLevel = bodyObject.getString("nextLevel").replaceAll("[^0-9]", "");
        TreeMap<String, Object> data = new TreeMap<>(){{
                put(EventConstants.MugenConfig.customerId, event.getCustomerId());
                put(EventConstants.MugenConfig.type, Integer.parseInt(nextLevel) > Integer.parseInt(oldLevel) ? "UPGRADE_SUCCEED" : "UPGRADE_FAIL");
                put(EventConstants.MugenConfig.progressInfo, JSON.toJSONString(progressInfo));
                put(EventConstants.MugenConfig.businessId, bodyObject.getString("orderId"));
                put(EventConstants.MugenConfig.saasId, bodyObject.getString("saasId"));
        }};
        String signature = SignatureUtil.generateSignature(data, kEventProperties.getQuestSync().get(EventConstants.MugenConfig.signature));
        data.put(EventConstants.MugenConfig.signature, signature);
        return data;
    }

    @Override
    public boolean isContinue(EventDO input) {
        return super.isContinue(input) &&
                kEventProperties.getQuestSyncEventName().containsKey(input.getName());
    }

    @Override
    public EventConstants.DeliveryStatus track(Map<String, Object> params) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        String appKey = kEventProperties.getQuestSync().get(EventConstants.MugenConfig.appKey);
        //App调用token构建
        headers.add(EventConstants.MugenConfig.appKey, appKey);
        try {
            return apiCall(HttpMethod.POST, EventConstants.DeliveryStatus.class, params, headers);
        } catch (Exception e) {
            log.warn("QuestsSyncTrackingExecutor track data error", e);
            return EventConstants.DeliveryStatus.FAIL;
        }
    }

    @Override
    public String endpoint() {
        return  kEventProperties.getQuestSync().get(EventConstants.MugenConfig.host) + "/s2/achievement/progress";
    }

    @Override
    public EventConstants.DeliveryChannel deliveryChannel() {
        return EventConstants.DeliveryChannel.QUEST_SYNC;
    }
}
