package com.kikitrade.kevent.common.exception;

import com.kikitrade.kevent.common.constant.EventMessageEnum;
import lombok.Data;

/**
 * @author: penuel
 * @date: 2021/04/28 10:02
 * @desc: TODO
 */
@Data
public class EventException extends Exception {

    private String code;
    private String key;
    private Object result;

    public EventException() {
    }

    public EventException(EventMessageEnum messageEnum) {
        super(messageEnum.getKey());
        this.code = messageEnum.getCode();
        this.key = messageEnum.getKey();
    }

    public EventException(EventMessageEnum messageEnum, Object result) {
        this(messageEnum);
        this.result = result;
    }

}
