package com.kikitrade.kevent.common.model;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Data
@Builder
public class ActivityEventMessage implements Serializable {

    /**
     * 事件code
     */
    private String eventCode;

    /**
     * 用户id
     */
    private String customerId;

    /**
     * 唯一id
     */
    private String globalUid;

    /**
     * 事件发生事件
     */
    private Long eventTime;

    /**
     * 文章id，达人id等
     */
    private String targetId;

    /**
     * 区分文章类型
     */
    private String verb;

    private String message;

    private String targetCustomerId;

    private String sourceCustomerId;

    private String callEvent;

    private String targetContentId;

    private Integer inc;
}
