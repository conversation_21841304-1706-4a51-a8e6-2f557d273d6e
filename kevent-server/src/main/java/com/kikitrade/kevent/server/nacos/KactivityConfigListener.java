package com.kikitrade.kevent.server.nacos;

import com.alibaba.nacos.api.config.listener.AbstractSharedListener;
import com.alibaba.nacos.api.exception.NacosException;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/11/7 17:14
 * @description: 监听kactivity-group下的配置变动
 */
@Component
@Slf4j
public class KactivityConfigListener {

    @Resource
    private PropertiesConfigService propertiesConfigService;

    private static final String ACTIVITY_TASK_CODE = "kactivity-task-code.json";

    @PostConstruct
    public void init(){
        try {
            addKactivityTaskCodeListener();
        } catch (NacosException e) {
            log.error("nacos addListener exception",e);
        }
    }

    /**
     * 任务code配置
     * @throws NacosException
     */
    private void addKactivityTaskCodeListener() throws NacosException{
        String config = propertiesConfigService.getConfigService().getConfigAndSignListener(ACTIVITY_TASK_CODE, "kactivity", 1000, new AbstractSharedListener() {
            @Override
            public void innerReceive(String s, String s1, String s2) {
                log.info("mes->kactivity-task-code change, dataId:{}, groupId:{}, content:{}", s, s1, s2);
                TaskCodeConfig.load(s2);
            }
        });
        log.info("mes->kactivity-task-code init, content:{}", config);
        TaskCodeConfig.load(config);
    }
}
