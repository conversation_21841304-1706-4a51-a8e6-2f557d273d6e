<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.kikitrade</groupId>
        <artifactId>kevent</artifactId>
        <version>2.0.2-SNAPSHOT</version>
    </parent>

    <artifactId>kevent-client</artifactId>
    <name>kevent-client</name>
    <description>Demo project for Spring Boot</description>
    <properties>
        <project.property.path>..</project.property.path>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.kikitrade</groupId>
            <artifactId>kevent-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kikitrade</groupId>
            <artifactId>kiki-ons-spring-boot-starter</artifactId>
            <scope>compile</scope>
        </dependency>
    </dependencies>

    <build>

        <plugins>
            <plugin>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.0.1</version>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
