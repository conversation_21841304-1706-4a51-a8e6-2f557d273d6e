package com.kikitrade.kevent.common.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/5/21 11:59
 */
@Data
public class EventCallDTO implements Serializable {

    /**
     * 事件名称 @EventConstants.EventName
     */
    private String name;

    /**
     * 必填项用户ID
     */
    private String customerId;

    /**
     * 回调的事件
     */
    private String callEvent;

    /**
     * 回调的参数
     */
    private Map<String, Object> body;
}
