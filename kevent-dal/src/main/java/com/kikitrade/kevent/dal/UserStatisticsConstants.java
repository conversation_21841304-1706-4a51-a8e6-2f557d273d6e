package com.kikitrade.kevent.dal;

/**
 * UserStatisticsConstants
 *
 * <AUTHOR>
 * @create 2022/6/8 7:06 下午
 * @modify
 */
public interface UserStatisticsConstants {

    String FIELD_CUSTOMER_ID = "customer_id";
    String FIELD_AC_STATIC_DATE = "ac_static_date";
    String FIELD_AC_AUM = "ac_aum";
    String FIELD_AC_FINANCE_AUM = "ac_finance_aum";
    String FIELD_AC_SPOT_AUM = "ac_spot_aum";
    String FIELD_AC_C2C_AUM = "ac_c2c_aum";
    String FIELD_AC_YESTERDAY_AUM = "ac_yesterday_aum";
    String FIELD_AC_YESTERDAY_FINANCE_AUM = "ac_yesterday_finance_aum";
    String FIELD_AC_YESTERDAY_SPOT_AUM = "ac_yesterday_spot_aum";
    String FIELD_AC_YESTERDAY_C2C_AUM = "ac_yesterday_c2c_aum";
    String FIELD_AC_ACCUMULATED_AMOUNT_DEPOSIT = "ac_accumulated_amount_deposit";
    String FIELD_AC_ACCUMULATED_AMOUNT_WITHDRAW = "ac_accumulated_amount_withdraw";
    String FIELD_AC_FIAT_ACCUMULATED_AMOUNT_DEPOSIT = "ac_fiat_accumulated_amount_deposit";
    String FIELD_AC_FIAT_ACCUMULATED_AMOUNT_WITHDRAW = "ac_fiat_accumulated_amount_withdraw";
    String FIELD_AC_CRYPTO_ACCUMULATED_AMOUNT_DEPOSIT = "ac_crypto_accumulated_amount_deposit";
    String FIELD_AC_CRYPTO_ACCUMULATED_AMOUNT_WITHDRAW = "ac_crypto_accumulated_amount_withdraw";
    String FIELD_AC_C2C_ACCUMULATED_AMOUNT_DEPOSIT = "ac_c2c_accumulated_amount_deposit";
    String FIELD_AC_C2C_ACCUMULATED_AMOUNT_WITHDRAW = "ac_c2c_accumulated_amount_withdraw";
    String FIELD_AC_CIRCLE_ACCUMULATED_AMOUNT_DEPOSIT = "ac_circle_accumulated_amount_deposit";
    String FIELD_AC_CIRCLE_ACCUMULATED_AMOUNT_WITHDRAW = "ac_circle_accumulated_amount_withdraw";
    String FIELD_AC_ACCUMULATED_AMOUNT_DEPOSIT_APPLY = "ac_accumulated_amount_deposit_apply";
    String FIELD_AC_ACCUMULATED_AMOUNT_WITHDRAW_APPLY = "ac_accumulated_amount_withdraw_apply";
    String FIELD_AC_PNL = "ac_pnl";
    String FIELD_AC_30DAYS_MAX_ORDER_CNT = "ac_30days_max_order_cnt";
    String FIELD_AC_30DAYS_MAX_CANCELLED_ORDER_CNT = "ac_30days_max_cancelled_order_cnt";
    String FIELD_AC_30DAYS_MAX_FILLED_ORDER_CNT = "ac_30days_max_filled_order_cnt";
    String FIELD_AC_REVISE_PASSWORD = "ac_revise_password";
    String FIELD_AC_REVISE_2FA = "ac_revise_2fa";
    String FIELD_AC_REVISE_EMAIL = "ac_revise_email";
    String FIELD_AC_REVISE_PHONE = "ac_revise_phone";
    String FIELD_AC_REVISE_DEVICE_ID = "ac_revise_device_id";


    /*enum Field {
        CUSTOMER_ID(FIELD_CUSTOMER_ID, false),
        AC_STATIC_DATE(FIELD_AC_STATIC_DATE, true),
        AC_AUM(FIELD_AC_AUM, true),
        AC_FINANCE_AUM(FIELD_AC_FINANCE_AUM, false),
        AC_SPOT_AUM(FIELD_AC_SPOT_AUM, false),
        AC_C2C_AUM(FIELD_AC_C2C_AUM, false),
        AC_YESTERDAY_AUM(FIELD_AC_YESTERDAY_AUM, true),
        AC_YESTERDAY_FINANCE_AUM(FIELD_AC_YESTERDAY_FINANCE_AUM, false),
        AC_YESTERDAY_SPOT_AUM(FIELD_AC_YESTERDAY_SPOT_AUM, false),
        AC_YESTERDAY_C2C_AUM(FIELD_AC_YESTERDAY_C2C_AUM, false),
        AC_ACCUMULATED_AMOUNT_DEPOSIT(FIELD_AC_ACCUMULATED_AMOUNT_DEPOSIT, true),
        AC_ACCUMULATED_AMOUNT_WITHDRAW(FIELD_AC_ACCUMULATED_AMOUNT_WITHDRAW, true),
        AC_FIAT_ACCUMULATED_AMOUNT_DEPOSIT(FIELD_AC_FIAT_ACCUMULATED_AMOUNT_DEPOSIT, false),
        AC_FIAT_ACCUMULATED_AMOUNT_WITHDRAW(FIELD_AC_FIAT_ACCUMULATED_AMOUNT_WITHDRAW, false),
        AC_CRYPTO_ACCUMULATED_AMOUNT_DEPOSIT(FIELD_AC_CRYPTO_ACCUMULATED_AMOUNT_DEPOSIT, false),
        AC_CRYPTO_ACCUMULATED_AMOUNT_WITHDRAW(FIELD_AC_CRYPTO_ACCUMULATED_AMOUNT_WITHDRAW, false),
        AC_C2C_ACCUMULATED_AMOUNT_DEPOSIT(FIELD_AC_C2C_ACCUMULATED_AMOUNT_DEPOSIT, false),
        AC_C2C_ACCUMULATED_AMOUNT_WITHDRAW(FIELD_AC_C2C_ACCUMULATED_AMOUNT_WITHDRAW, false),
        AC_CIRCLE_ACCUMULATED_AMOUNT_DEPOSIT(FIELD_AC_CIRCLE_ACCUMULATED_AMOUNT_DEPOSIT, false),
        AC_CIRCLE_ACCUMULATED_AMOUNT_WITHDRAW(FIELD_AC_CIRCLE_ACCUMULATED_AMOUNT_WITHDRAW, false),
        AC_PNL(FIELD_AC_PNL, true),
        AC_30DAYS_MAX_ORDER_CNT(FIELD_AC_30DAYS_MAX_ORDER_CNT, true),
        AC_30DAYS_MAX_CANCELLED_ORDER_CNT(FIELD_AC_30DAYS_MAX_CANCELLED_ORDER_CNT, true),
        AC_30DAYS_MAX_FILLED_ORDER_CNT(FIELD_AC_30DAYS_MAX_FILLED_ORDER_CNT, true),
        AC_REVISE_PASSWORD(FIELD_AC_REVISE_PASSWORD, true),
        AC_REVISE_2FA(FIELD_AC_REVISE_2FA, true),
        AC_REVISE_EMAIL(FIELD_AC_REVISE_EMAIL, true),
        ;

        private String key;
        private boolean include;

        Field(String key, boolean include) {
            this.key = key;
            this.include = include;
        }

        public String getKey() {
            return key;
        }

        public boolean isInclude() {
            return include;
        }
    }*/
}
