package com.kikitrade.kevent.client.autoconfigure;

import com.kikitrade.framework.ons.OnsProducer;
import com.kikitrade.kevent.client.EventClient;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.util.StringUtils;


/**
 * @author: penuel
 * @date: 2022/5/4 13:58
 * @desc: TODO
 */
@Configuration
@EnableConfigurationProperties(EventProperties.class)
@ConditionalOnBean(OnsProducer.class)
@ConditionalOnProperty(name = {"kiki.event.topic"})
public class EventAutoConfiguration {

    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private EventProperties eventProperties;
    @Autowired
    @Lazy
    private OnsProducer onsProducer;

    @Bean
    public EventClient eventClient() {
        EventClient client = new EventClient();
        client.setEventProperties(eventProperties);
        client.setOnsProducer(onsProducer);
        client.setDefaultSource(getApplicationId());
        return client;
    }


    private String getApplicationId() {
        String name = applicationContext.getEnvironment().getProperty("spring.application.name");
        if (StringUtils.isEmpty(name)) {
            name = applicationContext.getApplicationName();
        }
        return StringUtils.hasText(name) ? name : "application";
    }

}
