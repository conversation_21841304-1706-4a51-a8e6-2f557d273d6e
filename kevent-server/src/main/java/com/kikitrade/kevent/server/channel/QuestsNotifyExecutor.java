package com.kikitrade.kevent.server.channel;

import com.alibaba.fastjson2.JSON;
import com.kikitrade.kevent.client.autoconfigure.EventProperties;
import com.kikitrade.kevent.common.constant.EventConstants;
import com.kikitrade.kevent.common.exception.EventException;
import com.kikitrade.kevent.dal.model.EventDO;
import com.kikitrade.kevent.server.executor.impl.TrackingExecutor;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.Map;

@Slf4j
@Component("questsNotifyExecutor")
@ConditionalOnProperty(name = "kevent.quests.notify.api_url")
public class QuestsNotifyExecutor extends TrackingExecutor {

    @Resource
    private EventProperties eventProperties;

    @Override
    public Map<String, Object> mapping(EventDO event) throws EventException {
        return Map.of();
    }

    @Override
    public EventConstants.DeliveryStatus track(Map<String, Object> params) {
        return null;
    }

    @Override
    public String endpoint() {
        return JSON.toJSONString(eventProperties.getQuestsNotifyUrl());
    }

    @Override
    public EventConstants.DeliveryChannel deliveryChannel() {
        return EventConstants.DeliveryChannel.QUEST_NOTIFY;
    }

    @Override
    public boolean isContinue(EventDO input) {
        return super.isContinue(input) && input.getName().endsWith("_notify_");
    }
}
