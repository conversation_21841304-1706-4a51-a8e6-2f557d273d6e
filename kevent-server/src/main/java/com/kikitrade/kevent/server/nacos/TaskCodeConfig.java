package com.kikitrade.kevent.server.nacos;

import com.alibaba.fastjson.JSON;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Data;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/11/20 16:28
 */
@Data
public class TaskCodeConfig {

    private String code;
    private String mainCode;
    private int inc;
    private String desc;
    private String splitCode;

    private static final Map<String, TaskCodeConfig> map = new HashMap<>();

    public static void load(String config){
        List<TaskCodeConfig> codeConfig = JSON.parseArray(config, TaskCodeConfig.class);
        for(TaskCodeConfig config1 : codeConfig){
            map.put(config1.getCode(), config1);
        }
    }

    public static TaskCodeConfig getValue(String code){
        return map.get(code);
    }


    public static Map<String, TaskCodeConfig> getAll(){
        return map;
    }
}
