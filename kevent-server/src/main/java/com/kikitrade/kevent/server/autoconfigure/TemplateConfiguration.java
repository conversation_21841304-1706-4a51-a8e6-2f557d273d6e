package com.kikitrade.kevent.server.autoconfigure;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.thymeleaf.spring6.SpringTemplateEngine;
import org.thymeleaf.spring6.templateresolver.SpringResourceTemplateResolver;
import org.thymeleaf.templateresolver.ITemplateResolver;

/**
 * Template Configuration
 *
 * <AUTHOR>
 * @create 2022/6/28 3:36 下午
 * @modify
 */
@Configuration
public class TemplateConfiguration {

    @Autowired
    private KEventProperties kEventProperties;

    @Bean("springResourceTemplateResolver")
    public SpringResourceTemplateResolver springResourceTemplateResolver() {
        SpringResourceTemplateResolver springResourceTemplateResolver = new SpringResourceTemplateResolver();
        springResourceTemplateResolver.setCacheable(kEventProperties.isTemplateCacheEnable());
        springResourceTemplateResolver.setCacheTTLMs(kEventProperties.getTemplateCacheTtlMs());
        springResourceTemplateResolver.setCheckExistence(true);
        springResourceTemplateResolver.setPrefix("classpath:/templates/");
        return springResourceTemplateResolver;
    }

    @Bean
    public SpringTemplateEngine templateEngine(
            @Qualifier("springResourceTemplateResolver") ITemplateResolver springResourceTemplateResolver) {
        SpringTemplateEngine templateEngine = new SpringTemplateEngine();
        templateEngine.addTemplateResolver(springResourceTemplateResolver);
        // add fallback template
        templateEngine.setEnableSpringELCompiler(true);
        return templateEngine;
    }

}
