#spring.profiles.active=local
server.port=8080
kevent.saas-id=kiki
kevent.env=local

# dubbo config
dubbo.application.name=kevent
dubbo.application.id=kevent
dubbo.application.version=1.0.0
#dubbo.application.parameters[router]=traffic
#dubbo.application.parameters[traffic]=${TRAFFIC_TAG}
dubbo.protocol.server=netty
dubbo.protocol.name=dubbo
dubbo.protocol.port=20880
dubbo.protocol.threadpool=fixed
dubbo.protocol.threads=50
dubbo.protocol.queues=1000
dubbo.registry.address=zookeeper://localhost:2181
dubbo.consumer.group=kktd
dubbo.provider.group=kktd
dubbo.consumer.timeout=5000
dubbo.provider.timeout=5000
dubbo.reference.check=false
dubbo.consumer.check=false
dubbo.registry.check=false
spring.zipkin.base-url=http://api.dipbit.xyz:9411
spring.sleuth.sampler.probability=1.0

#ons
kiki.ons.address=http://onsaddr.mq-internet-access.mq-internet.aliyuncs.com:80
kiki.ons.group-id=GID_EVENT_KIKI_DEV
kiki.ons.env=_DEV
kiki.ons.tag=dev
kevent.ons-topic-event = T_EVENT_KIKI_DEV
#ots
kiki.ots.endpoint=https://KikiTradeBeta.ap-southeast-1.ots.aliyuncs.com
kiki.ots.instance-name=KikiTradeBeta
kiki.ots.skip-check-table-change=false

############### ignite  ###############
#kiki.ignite.namespace=kikitrade
#kiki.ignite.application-name=kevent
#kiki.ignite.local=true
#kiki.ignite.environment-namespace=kikitrade-dev
#kiki.ignite.client=false


#adjust config adjust sandbox/production
kevent.adjust-fix=true
kevent.adjust.android_app_token=sxs69b9anmyo
kevent.adjust.ios_app_token=xbc5e9hpl340
kevent.adjust.api_url=https://s2s.adjust.com/event
#adjust event token mapping
#beta event name :adjust_test
#kevent.adjust-event-token.android_adjust_test=46n1cl
#kevent.adjust-event-token.ios_adjust_test=o2lvb0
#Registration
kevent.adjust-event-token.android__registration__=7kim8j
kevent.adjust-event-token.ios__registration__=9nf0do
#KYC
kevent.adjust-event-token.android__kyc__=153abp
kevent.adjust-event-token.ios__kyc__=n023y5
#c2c_first_deposit
kevent.adjust-event-token.android__c2c_deposit__first=390yvo
kevent.adjust-event-token.ios__c2c_deposit__first=5ptxw9
#c2c_accumulative_deposit
kevent.adjust-event-token.android__c2c_deposit__=jsoz0t
kevent.adjust-event-token.ios__c2c_deposit__=lz8mw2
#
#circle_first_deposit
kevent.adjust-event-token.android__circle_deposit__first=vfri1a
kevent.adjust-event-token.ios__circle_deposit__first=i5fszc
#circle_accumulative_deposit
kevent.adjust-event-token.android__circle_deposit__=i7i3kb
kevent.adjust-event-token.ios__circle_deposit__=n8lnga

#crypto_first_deposit
kevent.adjust-event-token.android__crypto_deposit__first=e31x7b
kevent.adjust-event-token.ios__crypto_deposit__first=dhwlcy
#crypto_accumulative_deposit
kevent.adjust-event-token.android__crypto_deposit__=9pnyax
kevent.adjust-event-token.ios__crypto_deposit__=c2w41v

#credit_first_deposite
kevent.adjust-event-token.android__credit_deposit__first=7c7rmh
kevent.adjust-event-token.ios__credit_deposit__first=3apzy4
#credit_accumulative_deposit
kevent.adjust-event-token.android__credit_deposit__=aw166f
kevent.adjust-event-token.ios__credit_deposit__=utqi47

#facebook config
#prod
kevent.facebook.app-access-token=219948165709979|mKDDVa7U4A4j3QKP591b63I-_UQ
kevent.facebook.app-id=219948165709979

#test
#kevent.facebook.app-id=1286687031858045
#kevent.facebook.app-access-token=1286687031858045|fhewQkkwcA-ZW_e--BGpoiiX8mY

kevent.facebook.api_url=https://graph.facebook.com/{app-id}/activities

#facebook event name mapping
#Registration
kevent.facebook-event-name.__registration__=
#KYC
kevent.facebook-event-name.__kyc__=
#c2c_first_deposit
kevent.facebook-event-name.__c2c_deposit__first=
#c2c_accumulative_deposit
kevent.facebook-event-name.__c2c_deposit__=

#circle_first_deposit
kevent.facebook-event-name.__circle_deposit__first=
#circle_accumulative_deposit
kevent.facebook-event-name.__circle_deposit__=

#crypto_first_deposit
kevent.facebook-event-name.__crypto_deposit__first=
#crypto_accumulative_deposit
kevent.facebook-event-name.__crypto_deposit__=

#credit_first_deposite
kevent.facebook-event-name.__credit_deposit__first=
#credit_accumulative_deposit
kevent.facebook-event-name.__credit_deposit__=

#these error can be retry
kevent.error-retry.device_not_found=Device not found
kevent.error-retry.connection_timed_out=Connection timed out
kevent.error-retry.unexpected_end=Unexpected end of file from server
kevent.error-retry.illegal_device_identifiers=Request doesn't contain device identifiers
kevent.error-retry.internal_error=Internal error
kevent.error-retry.bad_gateway=502 Bad Gateway
kevent.error-retry.service_unavailable=Service Temporarily Unavailable



############### AliyunRisk Config  ###############
#AliyunRisk config
#prod
kevent.aliyun-risk.domain=saf.ap-southeast-1.aliyuncs.com
kevent.aliyun-risk.region=ap-southeast-1
kevent.aliyun-risk.version=2019-05-21

#AliyunRisk event name mapping
kevent.aliyun-risk-event-name.__c2c_withdraw_apply__=de_ayfjin4069
kevent.aliyun-risk-event-name.__c2c_withdraw__=de_ayfjin4069
kevent.aliyun-risk-event-name.__c2c_deposit__=de_ayfjin4069
kevent.aliyun-risk-event-name.__crypto_withdraw_apply__=de_ayfjin4069
kevent.aliyun-risk-event-name.__crypto_withdraw__=de_ayfjin4069
kevent.aliyun-risk-event-name.__crypto_deposit__=de_ayfjin4069
kevent.aliyun-risk-event-name.__circle_withdraw_apply__=de_ayfjin4069
kevent.aliyun-risk-event-name.__circle_withdraw__=de_ayfjin4069
kevent.aliyun-risk-event-name.__circle_deposit__=de_ayfjin4069
kevent.aliyun-risk-event-name.__order_created__=de_arginm0506
kevent.aliyun-risk-event-name.__order_cancelled__=de_arginm0506
kevent.aliyun-risk-event-name.__order_matched__=de_arginm0506
#userStatistics
kevent.aliyun-risk-event-name.__user_statistics_v1__=de_ayfjin4069
#redPocket
kevent.aliyun-risk-event-name.__release_red_pocket_v1__=de_ayfjin4069
kevent.aliyun-risk-event-name.__receive_red_pocket_v1__=de_ayfjin4069
#Registration
kevent.aliyun-risk-event-name.__registration__=
#Login
kevent.aliyun-risk-event-name.__login__=
#Verify
kevent.aliyun-risk-event-name.__verify__=

kevent.event-template.PayV1=Pay
kevent.event-template.TradeV1=Trade

##################elasticjob config ##################
elasticjob.reg-center.server-lists=localhost:2181
elasticjob.reg-center.namespace=kevent/jobs
elasticjob.reg-center.base-sleep-time-milliseconds=1000
elasticjob.reg-center.max-sleep-time-milliseconds=3000
elasticjob.reg-center.max-retries=5
elasticjob.reg-center.session-timeout-milliseconds=10000
elasticjob.reg-center.connection-timeout-milliseconds=10000

elasticjob.jobs.reportUserStatisticsJob.elastic-job-class=com.kikitrade.kevent.server.schedule.ReportUserStatisticsJob
elasticjob.jobs.reportUserStatisticsJob.cron=0 0 */1 * * ?
elasticjob.jobs.reportUserStatisticsJob.sharding-total-count=1
elasticjob.jobs.reportUserStatisticsJob.sharding-item-parameters=0=0
# reportLoginStatisticsJob???? 16???????????????????????0?????????????
elasticjob.jobs.reportLoginStatisticsJob.elastic-job-class=com.kikitrade.kevent.server.schedule.ReportLoginStatisticsJob
elasticjob.jobs.reportLoginStatisticsJob.cron=0 0 2,9,16 * * ?
elasticjob.jobs.reportLoginStatisticsJob.sharding-total-count=1
elasticjob.jobs.reportLoginStatisticsJob.sharding-item-parameters=0=0


############### GoogleAnalytics Config  ###############

# google-analytics api_url
kevent.google-analytics.api_url=https://www.google-analytics.com/mp/collect

# google-analytics token  ->  api_secret|firebase_app_id  or  api_secret|measurement_id
kevent.google-analytics-token.android=BQeWh_lbTtCXRB3VdCeInw|1:322470396590:android:b417567ba709fdd278633f
kevent.google-analytics-token.ios=19XYBBRhRZGua1fe_bxG0w|1:322470396590:ios:a10ead357dcb645578633f
kevent.google-analytics-token.web=W0SR9JuXR1600S9gdw036w|G-EESNECGGXJ
kevent.google-analytics-token.tablet=

# google-analytics event name mapping
kevent.google-analytics-event-name.__aspen_event_other__=aspen_event_other
kevent.google-analytics-event-name.__aspen_event_login__=aspen_event_login
kevent.google-analytics-event-name.__aspen_event_logout__=aspen_event_logout
kevent.google-analytics-event-name.__aspen_event_userregister__=aspen_event_userregister
kevent.google-analytics-event-name.__aspen_event_restpassword__=aspen_event_restpassword
kevent.google-analytics-event-name.__aspen_event_osstoken__=aspen_event_osstoken
kevent.google-analytics-event-name.__aspen_event_addbank__=aspen_event_addbank
kevent.google-analytics-event-name.__aspen_event_delbank__=aspen_event_delbank
kevent.google-analytics-event-name.__aspen_event_addcirclebank__=aspen_event_addcirclebank
kevent.google-analytics-event-name.__aspen_event_updatecirclebank__=aspen_event_updatecirclebank
kevent.google-analytics-event-name.__aspen_event_dingtalknotify__=aspen_event_dingtalknotify
kevent.google-analytics-event-name.__aspen_event_subscribe__=aspen_event_subscribe
kevent.google-analytics-event-name.__aspen_event_redeem__=aspen_event_redeem
kevent.google-analytics-event-name.__aspen_event_generategooglekey__=aspen_event_generategooglekey
kevent.google-analytics-event-name.__aspen_event_bindgoogle__=aspen_event_bindgoogle
kevent.google-analytics-event-name.__aspen_event_removebindgoogle__=aspen_event_removebindgoogle
kevent.google-analytics-event-name.__aspen_event_verifygooglecode__=aspen_event_verifygooglecode
kevent.google-analytics-event-name.__aspen_event_verifygooglephotocode__=aspen_event_verifygooglephotocode
kevent.google-analytics-event-name.__aspen_event_createkyc__=aspen_event_createkyc
kevent.google-analytics-event-name.__aspen_event_updatekyc__=aspen_event_updatekyc
kevent.google-analytics-event-name.__aspen_event_addstrategy__=aspen_event_addstrategy
kevent.google-analytics-event-name.__aspen_event_newintentiondepositorder__=aspen_event_newintentiondepositorder
kevent.google-analytics-event-name.__aspen_event_newintentionwithdraworder__=aspen_event_newintentionwithdraworder
kevent.google-analytics-event-name.__aspen_event_placeorder__=aspen_event_placeorder
kevent.google-analytics-event-name.__aspen_event_cancelorder__=aspen_event_cancelorder
kevent.google-analytics-event-name.__aspen_event_addwithdrawaddress__=aspen_event_addwithdrawaddress
kevent.google-analytics-event-name.__aspen_event_applywithdraw__=aspen_event_applywithdraw
kevent.google-analytics-event-name.__aspen_event_applyotcdeposit__=aspen_event_applyotcdeposit
kevent.google-analytics-event-name.__aspen_event_applyotcwithdraw__=aspen_event_applyotcwithdraw
kevent.google-analytics-event-name.__aspen_event_prewithdraw__=aspen_event_prewithdraw
kevent.google-analytics-event-name.__aspen_event_applycirclewithdraw__=aspen_event_applycirclewithdraw
kevent.google-analytics-event-name.__aspen_event_fireblockswebhook__=aspen_event_fireblockswebhook
kevent.google-analytics-event-name.__aspen_event_circlewebhook__=aspen_event_circlewebhook


kevent.activity-topic-name=
# appsFlyer
kevent.apps-flyer-event-name.__order_matched__=af_accumulative_spottrade

kevent.enable-block=true


