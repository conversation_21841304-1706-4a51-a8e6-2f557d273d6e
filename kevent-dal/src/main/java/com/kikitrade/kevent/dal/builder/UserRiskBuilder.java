package com.kikitrade.kevent.dal.builder;

import com.kikitrade.kevent.dal.model.UserRiskDO;

public interface UserRiskBuilder {

    /**
     * Get risk info
     *
     * @param customerId
     * @return
     */
    UserRiskDO get(String customerId);

    /**
     * Create risk info
     *
     * @param userRiskDO
     * @return
     */
    boolean create(UserRiskDO userRiskDO);

    /**
     * Update risk basic info
     *
     * @param userRiskDO
     * @return
     */
    boolean updateRiskInfo(UserRiskDO userRiskDO);

}
