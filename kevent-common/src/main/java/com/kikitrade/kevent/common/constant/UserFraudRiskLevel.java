package com.kikitrade.kevent.common.constant;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * UserFraudRiskLevel
 *
 * <AUTHOR>
 * @create 2022/6/16 2:20 下午
 * @modify
 */
public enum UserFraudRiskLevel {
    UNKNOWN(0, -100, -1),
    NONE(1, -1, 0),
    <PERSON>OW(2, 0, 35),
    MEDIUM(3, 35, 65),
    HIGH(4, 65, 85),
    CRITICAL(5, 85, 1000);

    private int code;
    private double fromExclusive;
    private double toInclusive;

    UserFraudRiskLevel(int code, double fromExclusive, double toInclusive) {
        this.code = code;
        this.fromExclusive = fromExclusive;
        this.toInclusive = toInclusive;
    }

    public int getCode() {
        return code;
    }

    public static UserFraudRiskLevel fromCode(int code) {
        return Arrays.stream(UserFraudRiskLevel.values())
                .filter(a -> a.code == code)
                .findFirst().orElse(NONE);
    }

    public static UserFraudRiskLevel fromScore(double score) {
        return Arrays.stream(UserFraudRiskLevel.values())
                .filter(a -> score > a.fromExclusive && score <= a.toInclusive)
                .findFirst().orElse(NONE);
    }

    public static UserFraudRiskLevel fromName(String name) {
        return Arrays.stream(UserFraudRiskLevel.values())
                .filter(a -> StringUtils.equalsIgnoreCase(a.name(), name))
                .findFirst().orElse(NONE);
    }

}
