package com.kikitrade.kevent.server;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.kikitrade.framework.common.model.PageResult;
import com.kikitrade.framework.common.model.TokenPage;
import com.kikitrade.kevent.client.EventClient;
import com.kikitrade.kevent.common.constant.EventConstants;
import com.kikitrade.kevent.common.model.EventDTO;
import com.kikitrade.kevent.common.util.DateUtil;
import com.kikitrade.kevent.dal.builder.EventStoreBuilder;
import com.kikitrade.kevent.dal.builder.UserStatisticsBuilder;
import com.kikitrade.kevent.dal.model.EventDO;
import com.kikitrade.kevent.dal.model.UserStatisticsDO;
import com.kikitrade.kevent.server.channel.AdjustTrackingExecutor;
import com.kikitrade.kevent.server.channel.AliyunRiskTrackingExecutor;
import com.kikitrade.kevent.server.channel.FaceBookTrackingExecutor;
import com.kikitrade.kevent.server.mq.EventOnsListener;
import com.kikitrade.kevent.server.service.EventService;
import com.kikitrade.kevent.server.service.TemplateService;
import com.kikitrade.kevent.server.service.TemplateTask;
import com.kikitrade.kevent.server.util.TwoTuple;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.TestPropertySource;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import jakarta.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


@Slf4j
@SpringBootTest(classes = KEventServerApplication.class)
@TestPropertySource(locations = "classpath:application-local.properties")
public class KEventServerApplicationTests {

    @Autowired
    EventStoreBuilder eventStoreBuilder;
    @Autowired
    private EventService eventService;
    @Autowired
    private EventOnsListener eventOnsListener;

    @MockBean
    private EventClient eventClient;

    @Autowired
    private UserStatisticsBuilder userStatisticsBuilder;

    @Test
    void contextLoads() {
    }

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private TemplateService templateService;

    @Test
    void testAdjust() {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/x-www-form-urlencoded");
        headers.add("Authorization", "Bearer v8s5opi0zs45607l8h8c48wak2vxb5oe");

        MultiValueMap<String, Object> postParameters = new LinkedMultiValueMap<>();
        postParameters.add("s2s", 1);
        postParameters.add("event_token", "46n1cl");
        postParameters.add("app_token", "sxs69b9anmyo");
        postParameters.add("gps_adid", "2c1416e3-3b9c-4b1d-93af-cd95770e045e");
        System.out.println(postParameters);
        HttpEntity<MultiValueMap<String, Object>> httpEntity = new HttpEntity<>(postParameters, headers);

        String url = "https://s2s.adjust.com/event";
        ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);
        System.out.println(responseEntity);
    }

    @Test
    void testFaceBook() {
        MultiValueMap<String, Object> postParameters = new LinkedMultiValueMap<>();
        postParameters.add("event", "CUSTOM_APP_EVENTS");
        postParameters.add("advertiser_id", "ab41db74-0932-40e7-a65a-4ba6b3c8d8f4");//
        postParameters.add("advertiser_tracking_enabled", "1");
        postParameters.add("application_tracking_enabled", "1");
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("_eventName", "S2S_test");
        jsonObject.put("fb_description", "S2S");

        postParameters.add("custom_events", Lists.newArrayList(jsonObject.toJSONString()));
        postParameters.add("app-access-token", "219948165709979|mKDDVa7U4A4j3QKP591b63I-_UQ");

        HttpEntity<MultiValueMap<String, Object>> httpEntity = new HttpEntity<>(postParameters, null);

        String url = "https://graph.facebook.com/219948165709979/activities";
        ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);
        System.out.println(responseEntity);
    }

    @Test
    void testAppsFlyer() {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        headers.add("authentication", "b8K9KEVgf7CMfyurjH7Zp7");

        MultiValueMap<String, Object> postParameters = new LinkedMultiValueMap<>();
        postParameters.add("customerUserId", "1234567890abcdef");
        postParameters.add("afUserId", "46n1cl");
        postParameters.add("webDevKey", "b8K9KEVgf7CMfyurjH7Zp7");
        postParameters.add("ip", "127.0.0.1");
        postParameters.add("eventType", "EVENT");
        postParameters.add("timestamp", System.currentTimeMillis());
        postParameters.add("eventName", "my_wev_event");

        System.out.println(postParameters);
        HttpEntity<MultiValueMap<String, Object>> httpEntity = new HttpEntity<>(postParameters, headers);

        String url = "https://api2.appsflyer.com/inappevent/com.abl.vibra";
        ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);
        System.out.println(responseEntity);
    }


    @Resource
    AdjustTrackingExecutor adjustTrackingExecutor;

    @Test
    void testAdjustEvent() {
        EventDO eventDO = new EventDO();
        eventDO.setId("1");
        eventDO.setCreated(new Date());
        eventDO.setName("registration");
        JSONObject params = new JSONObject();
//        params.put("revenue", 11.2);
//        params.put("currency", "XRP");
//        String body = params.toJSONString();
//        eventDO.setBody(body);
        eventDO.setUid("1");
        eventDO.setCustomerId("12132");
//        eventDO.setTime(DateUtils.addHours(DateUtils.addDays(new Date(), -9),-19).getTime());
        String date = "2022-07-21";
        System.out.println("-------开始-------");
//        Long LDate = 1658448000000L;
        Long LDate = 1658473200000L;

        eventDO.setTime(LDate);
//
        adjustTrackingExecutor.doExecute(eventDO);
//        System.out.println("----------1");
//        eventDO.setTime(DateUtils.addHours(getStartOfDay(date), -24).getTime());
//        adjustTrackingExecutor.doExecute(eventDO);
//        System.out.println("----------2");
//        eventDO.setTime(DateUtils.addHours(getStartOfDay(date), -23).getTime());
//        adjustTrackingExecutor.doExecute(eventDO);
//
//        System.out.println("----------3");
//        eventDO.setTime(DateUtils.addHours(getStartOfDay(date), -22).getTime());
//        adjustTrackingExecutor.doExecute(eventDO);
//
//        System.out.println("----------4");
//        eventDO.setTime(DateUtils.addHours(getStartOfDay(date), -21).getTime());
//        adjustTrackingExecutor.doExecute(eventDO);
//        System.out.println("----------5");
//        eventDO.setTime(DateUtils.addHours(getStartOfDay(date), -20).getTime());
//        adjustTrackingExecutor.doExecute(eventDO);
//        System.out.println("----------6");
//        eventDO.setTime(DateUtils.addHours(getStartOfDay(date), -14).getTime());
//        adjustTrackingExecutor.doExecute(eventDO);
//        System.out.println("----------7");
//        eventDO.setTime(DateUtils.addHours(getStartOfDay(date), -13).getTime());
//        adjustTrackingExecutor.doExecute(eventDO);
//        System.out.println("----------8");
//        eventDO.setTime(DateUtils.addHours(getStartOfDay(date), -12).getTime());
//        adjustTrackingExecutor.doExecute(eventDO);
//        System.out.println("----------9");
//        eventDO.setTime(DateUtils.addHours(getStartOfDay(date), -11).getTime());
//        adjustTrackingExecutor.doExecute(eventDO);

//        long a = LDate - 24 * 60 * 60 * 1000;


        System.out.println("----------24");
        eventDO.setTime(LDate - 24 * 60 * 60 * 1000);
        adjustTrackingExecutor.doExecute(eventDO);

        System.out.println("----------23");
        eventDO.setTime(LDate - 23 * 60 * 60 * 1000);
        adjustTrackingExecutor.doExecute(eventDO);

        System.out.println("----------22");
        eventDO.setTime(LDate - 22 * 60 * 60 * 1000);
        adjustTrackingExecutor.doExecute(eventDO);

        System.out.println("----------21");
        eventDO.setTime(LDate - 21 * 60 * 60 * 1000);
        adjustTrackingExecutor.doExecute(eventDO);

        System.out.println("----------20");
        eventDO.setTime(LDate - 20 * 60 * 60 * 1000);
        adjustTrackingExecutor.doExecute(eventDO);


        System.out.println("----------19");
        eventDO.setTime(LDate - 19 * 60 * 60 * 1000);
        adjustTrackingExecutor.doExecute(eventDO);


        System.out.println("----------18");
        eventDO.setTime(LDate - 18 * 60 * 60 * 1000);
        adjustTrackingExecutor.doExecute(eventDO);

        System.out.println("----------17");
        eventDO.setTime(LDate - 17 * 60 * 60 * 1000);
        adjustTrackingExecutor.doExecute(eventDO);
        System.out.println("----------16");
        eventDO.setTime(LDate - 16 * 60 * 60 * 1000);
        adjustTrackingExecutor.doExecute(eventDO);

        System.out.println("----------15");
        eventDO.setTime(LDate - 15 * 60 * 60 * 1000);
        adjustTrackingExecutor.doExecute(eventDO);

        System.out.println("----------14");
        eventDO.setTime(LDate - 14 * 60 * 60 * 1000);
        adjustTrackingExecutor.doExecute(eventDO);


        System.out.println("----------13");
        eventDO.setTime(LDate - 13 * 60 * 60 * 1000);
        adjustTrackingExecutor.doExecute(eventDO);
        System.out.println("----------12");
        eventDO.setTime(LDate - 12 * 60 * 60 * 1000);
        adjustTrackingExecutor.doExecute(eventDO);
        System.out.println("----------11");
        eventDO.setTime(LDate - 11 * 60 * 60 * 1000);
        adjustTrackingExecutor.doExecute(eventDO);
        System.out.println("----------10");
        eventDO.setTime(LDate - 10 * 60 * 60 * 1000);
        adjustTrackingExecutor.doExecute(eventDO);
        System.out.println("----------9");
        eventDO.setTime(LDate - 9 * 60 * 60 * 1000);
        adjustTrackingExecutor.doExecute(eventDO);
        System.out.println("----------8");
        eventDO.setTime(LDate - 8 * 60 * 60 * 1000);
        adjustTrackingExecutor.doExecute(eventDO);

        System.out.println("----------7");
        eventDO.setTime(LDate - 7 * 60 * 60 * 1000);
        adjustTrackingExecutor.doExecute(eventDO);

        System.out.println("----------6");
        eventDO.setTime(LDate - 6 * 60 * 60 * 1000);
        adjustTrackingExecutor.doExecute(eventDO);


        System.out.println("----------5");
        eventDO.setTime(LDate - 5 * 60 * 60 * 1000);
        adjustTrackingExecutor.doExecute(eventDO);

        System.out.println("----------4");
        eventDO.setTime(LDate - 4 * 60 * 60 * 1000);
        adjustTrackingExecutor.doExecute(eventDO);
        System.out.println("----------3");
        eventDO.setTime(LDate - 3 * 60 * 60 * 1000);
        adjustTrackingExecutor.doExecute(eventDO);


        System.out.println("----------2");
        eventDO.setTime(LDate - 2 * 60 * 60 * 1000);
        adjustTrackingExecutor.doExecute(eventDO);
        System.out.println("----------1");
        eventDO.setTime(LDate - 1 * 60 * 60 * 1000);
        adjustTrackingExecutor.doExecute(eventDO);
    }

    public static String yyyyMMdd = "yyyy-MM-dd";
    public static String yyyyMMddHHmmss_str = "yyyyMMddHHmmss";
    public static String yyyyMMddHHmmssSSS_str = "yyyyMMddHHmmssSSS";

    @Test
    void testFixAdjustEvent() throws ParseException {
        JSONObject jsonObject = new JSONObject();
        List<String> EventFailIds = new ArrayList<>();
        try {
            Date dateStart = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse("2020-11-01 08:30:20");
            Date dateEnd = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse("2022-11-01 08:30:20");
            TokenPage<EventDO> eventDOTokenPage = null;
            String nextToken = "";
//            while (true){
//                eventDOTokenPage = eventStoreBuilder.getEventByTime(dateStart.getTime(),dateEnd.getTime(), EventConstants.DeliveryChannel.ADJUST, EventConstants.DeliveryStatus.SUCCESS,nextToken,5);
//                if (eventDOTokenPage == null || eventDOTokenPage.getRows() == null || eventDOTokenPage.getRows().size() <=0 ){
//                    break;
//                }
//                eventDOTokenPage.getRows().forEach(event -> {
//                    if(adjustTrackingExecutor.isContinue(event)){
//                        //异步执行
//                        EventConstants.DeliveryStatus deliveryStatus = adjustTrackingExecutor.executeFix((EventDO) event);
//                        log.info("dataFix:{}", JSON.toJSONString(event),"deliveryStatus:{}",deliveryStatus.name());
//                        if (deliveryStatus.equals(EventConstants.DeliveryStatus.FAIL)){
//                            EventFailIds.add((event).getId());
//                        }
//                    }
//                });
//                nextToken = eventDOTokenPage.getNextToken();
//                if (nextToken == null){
//                    break;
//                }
//            }
        } catch (Exception e) {
            log.error("dataFix error", e);
            jsonObject.put("fail", e.getMessage());
        }
        jsonObject.put("success", "OK");
        jsonObject.put("EventFailIds", EventFailIds);
        System.out.println(JSON.toJSONString(jsonObject));
//        eventStoreBuilder.updateStatusByType("kcustomer_registration_2022061400000000000001", EventConstants.DeliveryChannel.ALIYUN_RISK, EventConstants.DeliveryStatus.PROCESSING);
    }

    @Resource
    FaceBookTrackingExecutor faceBookTrackingExecutor;

    @Test
    void testFacebookEventMulti() {
        for (int i = 0; i < 10; i++) {
            testFacebookEvent();
        }
    }

    @Test
    void testFacebookEvent() {
        EventDO eventDO = new EventDO();
        eventDO.setId("2");
        eventDO.setCreated(new Date());
        eventDO.setName("kyc_first");
        eventDO.setUid("2022011106040982602001");
        JSONObject params = new JSONObject();
        params.put("fb_description", "test the s2s");
//        params.put("fb_content_id",Lists.newArrayList("123","456"));

//        JSONObject content1 = new JSONObject();
//        content1.put("id","123");
//        content1.put("quantity","1.1");
//        JSONObject content2 = new JSONObject();
//        content2.put("id","3456");
//        content2.put("quantity","2.2");
//        params.put("fb_content",Lists.newArrayList(content1,content2));
////
//        params.put("fb_num_items","3");
        params.put("revenue", 3.1);
        params.put("currency", "USD");
//        params.put("unique_id","324sae33344fds");

        String body = params.toJSONString();
        eventDO.setBody(body);
        eventDO.setUid("4");
        eventDO.setTime(System.currentTimeMillis());
        faceBookTrackingExecutor.doExecute(eventDO);
    }

    @Resource
    private AliyunRiskTrackingExecutor aliyunRiskTrackingExecutor;

    @Test
    void testAliyunRiskEvent() {
        EventDO eventDO = new EventDO();
        eventDO.setId("2");
        eventDO.setCreated(new Date());
        eventDO.setName("de_aflucw5770");
        eventDO.setUid("2022011106040982602001");
        JSONObject params = new JSONObject();
        params.put("customer_id", "allen");
        params.put("amount", 3.1);
        params.put("currency", "USD");

        String body = params.toJSONString();
        eventDO.setBody(body);
        eventDO.setTime(System.currentTimeMillis());
        aliyunRiskTrackingExecutor.doExecute(eventDO);
    }

    @Test
    void consume() {
        EventDO eventDO = new EventDO();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("id", "hfja");
        jsonObject.put("name", "ly-test");
        eventDO.setBody(jsonObject.toJSONString());
        eventDO.setName("kyc");
        eventDO.setCreated(new Date());
        eventDO.setModified(new Date());
        eventDO.setTime(System.currentTimeMillis());
        eventDO.setId("123456789");
        boolean process = eventService.process(eventDO);
////        byte[] body = "".getBytes(StandardCharsets.UTF_8);
//        Message message = new Message("","",body);
//        ConsumeContext consumeContext= new ConsumeContext();
//        eventOnsListener.consume(message,consumeContext);
    }

    @Test
    void EventOnsListenerConsume() {
        EventDTO eventDO = new EventDTO();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("id", "hfja");
        jsonObject.put("name", "ly-test");
        eventDO.setName("kyc");
        eventDO.setTime(System.currentTimeMillis());
        eventDO.setSource("ly-test-source");
        Message message = new Message("ly-test", "ly-test-tag", JSONObject.toJSONString(eventDO).getBytes());
        message.setMsgID("00000000001");
        ConsumeContext consumeContext = new ConsumeContext();
        System.out.println(JSONObject.toJSON(eventOnsListener.consume(message, consumeContext)));
    }

    @Test
    void pushData() {
        for (int i = 0; i < 10; i++) {
            EventDTO eventDO = new EventDTO();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("id", "hfja");
            jsonObject.put("name", "ly-test");
            eventDO.setName("kyc");
            eventDO.setTime(System.currentTimeMillis());
            eventClient.push(eventDO);
            System.out.println(JSONObject.toJSON(eventDO));
        }
        System.out.println(eventClient.getDefaultSource());

    }

    @Test
    public void testRender() {
        TemplateTask task = new TemplateTask();
        task.setTemplateName("TestEvent.tpl");
        // replace parameters into tpl
        Map<String, Object> parameterMap = new HashMap<>();
        parameterMap.put("amount", "10.12");
        parameterMap.put("acCircleAccumulatedAmountWithdraw", "1.23");
        parameterMap.put("customer_id", "allen.qin");
        task.setParameterMap(parameterMap);

        String body = templateService.render(task);
        log.info("RiskEngine request body={} params={}", JSON.toJSONString(body), JSON.toJSONString(parameterMap));
    }

    public static void main(String[] args) {
//        String str = "android@@registration_23wds-2321^2838*_32_wdsdw2SA&*@@first@@222";
//        String rgex = "@@(.*?)@@";
//        Pattern pattern = Pattern.compile(rgex);
//        Matcher m = pattern.matcher(str);
//        while(m.find()){
//            System.out.println(m.group(1));
//        }

        String str2 = "234eA@";
        String rgex2 = "^[0-9a-zA-Z_-]{1,}$";
        Pattern pattern2 = Pattern.compile(rgex2);
        Matcher m2 = pattern2.matcher(str2);
        while (m2.find()) {
            System.out.println(m2.group(1));
            String str = "android__registration_23wds-2321^2838*_32_wdsdw2SA&*__";
            String rgex = "__(.*?)__";
            Pattern pattern = Pattern.compile(rgex);
            Matcher m = pattern.matcher(str);
            while (m.find()) {
                System.out.println(m.group(1));
            }
            System.out.println(parseOriginalName("kyc_first").first);
            System.out.println(parseOriginalName("kyc_first").getFirst());
            System.out.println(parseOriginalName("kyc_first").getSecond());
            System.out.println(parseOriginalName("kyc").getSecond().length());
        }

    }

    public static TwoTuple<String, String> parseOriginalName(String name) {
        if (name.endsWith("_first")) {
            String originalName = name.substring(0, name.lastIndexOf("_first"));
            //首次事件的后缀为first
            return new TwoTuple<>(originalName, "first");
        }
        //默认无后缀
        return new TwoTuple<>(name, "");
    }

    @Test
    void testOts() {
//        TokenPage<EventDO> eventDOTokenPage = eventStoreBuilder.listEventByTime(1640997222000L, 1673483622000L,  EventConstants.DeliveryChannel.ADJUST, EventConstants.DeliveryStatus.FAIL, "", 10);

        EventConstants.DeliveryStatus result = eventStoreBuilder.getDeliveryStatusByType("kpay_crypto_deposit_202205181546123456789", EventConstants.DeliveryChannel.ADJUST);
        System.out.println(JSON.toJSONString(result));
        boolean b = eventStoreBuilder.updateStatusByType("kpay_crypto_deposit_202205181546123456789", EventConstants.DeliveryChannel.ADJUST, EventConstants.DeliveryStatus.PROCESSING);

    }

    @Test
    public void getLatest(){
//        UserStatisticsDO data = userStatisticsBuilder.getLatest("2022032117580659571514");
//        System.out.println(data);
        Long dateEnd = System.currentTimeMillis();
        Long dateStart = DateUtil.addDayToDate(new Date(dateEnd), -8).getTime();
        List<EventConstants.EventName> reportEventList = new ArrayList<>();
        reportEventList.add(EventConstants.EventName.LOGIN);
        TokenPage<EventDO> eventDOTokenPage = eventStoreBuilder.filterEventByTime(dateStart, dateEnd, EventConstants.DeliveryChannel.ALIYUN_RISK, EventConstants.DeliveryStatus.SUCCESS, reportEventList, null, 100);
        if (eventDOTokenPage == null || eventDOTokenPage.getRows() == null || eventDOTokenPage.getRows().size() <= 0) {
            return;
        }
        Set<String> customerIds = eventDOTokenPage.getRows().stream().collect(Collectors.groupingBy(EventDO::getCustomerId)).keySet();
        System.out.println(customerIds);
    }
}
