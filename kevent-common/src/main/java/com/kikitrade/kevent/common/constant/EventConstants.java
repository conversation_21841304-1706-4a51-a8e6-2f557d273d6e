package com.kikitrade.kevent.common.constant;

import com.google.common.collect.Lists;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: penuel
 * @date: 2022/5/4 14:44
 * @desc: TODO
 */
public interface EventConstants {

    String TOPIC_EVENT = "T_EVENT";

    enum EventName {
        KYC("kyc", "kyc认证"),
        KYC_L1("kyc_l1", "kycL1认证"),
        KYC_L2("kyc_l2", "kycL2认证"),
        REGISTRATION("registration", "用户注册", RiskConstants.Template.Registration),
        LOGIN("login","登录", RiskConstants.Template.Login, true),
        VERIFY("verify","验证码", RiskConstants.Template.Verify),
        // C2C event definition
        C2C_DEPOSIT_APPLY("c2c_deposit_apply", "c2c入金创建", RiskConstants.Template.PayV1,true),
        C2C_DEPOSIT_CANCEL("c2c_deposit_cancel", "c2c入金撤销", RiskConstants.Template.PayV1,true),
        C2C_DEPOSIT("c2c_deposit", "c2c入金", RiskConstants.Template.PayV1,true),
        C2C_WITHDRAW_APPLY("c2c_withdraw_apply", "c2c出金创建", RiskConstants.Template.PayV1,true),
        C2C_WITHDRAW_CANCEL("c2c_withdraw_cancel", "c2c出金撤销", RiskConstants.Template.PayV1,true),
        C2C_WITHDRAW("c2c_withdraw", "c2c出金", RiskConstants.Template.PayV1,true),
        // Crypto event definition
        CRYPTO_DEPOSIT("crypto_deposit", "crypto入金", RiskConstants.Template.PayV1, true),
        CRYPTO_WITHDRAW_APPLY("crypto_withdraw_apply", "crypto出金申请", RiskConstants.Template.PayV1,true),
        CRYPTO_WITHDRAW_CANCEL("crypto_withdraw_cancel", "crypto出金撤销", RiskConstants.Template.PayV1,true),
        CRYPTO_WITHDRAW_REJECT("crypto_withdraw_reject", "crypto出金拒绝", RiskConstants.Template.PayV1,true),
        CRYPTO_WITHDRAW("crypto_withdraw", "crypto出金",RiskConstants.Template.PayV1,true),
        // Credit event definition
        CREDIT_DEPOSIT("credit_deposit", "credit入金", RiskConstants.Template.PayV1,true),
        // Crypto event definition
        CIRCLE_DEPOSIT("circle_deposit", "circle入金", RiskConstants.Template.PayV1,true),
        CIRCLE_WITHDRAW_APPLY("circle_withdraw_apply", "circle出金申请", RiskConstants.Template.PayV1,true),
        CIRCLE_WITHDRAW_CANCEL("circle_withdraw_cancel", "circle出金撤销", RiskConstants.Template.PayV1,true),
        CIRCLE_WITHDRAW_REJECT("circle_withdraw_reject", "circle出金拒绝", RiskConstants.Template.PayV1,true),
        CIRCLE_WITHDRAW("circle_withdraw", "circle出金", RiskConstants.Template.PayV1, true),
        // Trade event definition
        ORDER_CREATED("order_created", "交易下单", RiskConstants.Template.TradeV1, true),
        ORDER_CANCELLED("order_cancelled", "交易撤单", RiskConstants.Template.TradeV1, true),
        ORDER_MATCHED("order_matched", "交易成交", RiskConstants.Template.TradeV1,true),
        //social
        FOLLOW("follow", "关注"),
        LIKE("like", "赞"),
        REMOVE_LIKE("remove_like", "取消赞"),
        SHARE("share", "分享"),
        SHARE_POST("share_post", "分享"),
        COMMENT("comment_up", "评论"),
        REPLY("reply", "回复"),
        POST("post", "发帖"),
        POST_SPLENDID("post_splendid", "贴文加精品"),
        FOLLOWED("followed", "被关注"),
        LIKED("liked", "被点赞"),
        // 离线数据
        USER_STATISTICS("user_statistics", "用户离线数据", RiskConstants.Template.Statistics),
        // activity
        RELEASE_RED_POCKET("release_red_pocket", "发放红包", RiskConstants.Template.RedPocket, true),
        RECEIVE_RED_POCKET("receive_red_pocket", "领取红包", RiskConstants.Template.RedPocket, true),
        // 解禁
        UNBLOCK("unblock", "解禁", RiskConstants.Template.Unblock),
        INVITED_REGISTER("invited_register","被邀请注册"),
        VOTE_1("open_reaction_vote_1", "顶"),
        PURCHASE_MEMBERSHIP("purchase_membership", "购买会员"),
        INVITED_PURCHASE_MEMBERSHIP("invited_purchase_membership", "被邀请者购买会员")

        ;
        private String name;
        private String desc;
        private Boolean reportStatistics = false;
        private RiskConstants.Template templateName;

        EventName(String name, String desc) {
            this.name = name;
            this.desc = desc;
        }

        EventName(String name, String desc, RiskConstants.Template templateName){
            this(name, desc);
            this.templateName = templateName;
        }

        EventName(String name, String desc,  RiskConstants.Template templateName, Boolean reportStatistics){
            this(name, desc, templateName);
            this.reportStatistics = reportStatistics;
        }

        public String getName() {
            return name;
        }

        public Boolean getReportStatistics() {
            return reportStatistics;
        }

        public RiskConstants.Template getTemplateName() {
            return templateName;
        }

        public static List<String> selfSkipEventNames() {
            return Lists.newArrayList(SHARE_POST, // mergeTrack
                    FOLLOWED, LIKED, LIKE, // splitTrack
                    COMMENT, REPLY) // track
                    .stream().map(EventName::getName).collect(Collectors.toList());
        }

        public static EventName getByName(String name){
            return Arrays.stream(values()).filter(eventName -> eventName.getName().equals(name)).findFirst().orElse(null);
        }
    }

    enum DeliveryStatus {
        PROCESSING,
        SUCCESS,
        FAIL,
        UNKNOWN,
        WAIT_CALL
        ;
    }

    enum DeliveryChannel {
        ADJUST,
        FACEBOOK,
        APPSFLYER,
        SLS,
        ALIYUN_RISK,
        GOOGLE_ANALYTICS,
        ACTIVITY,
        QUEST_SYNC,
        QUEST_NOTIFY,
        TREX_SYNC
        ;
    }

    /**
     * 渠道参数
     */
    interface FacebookConstant {
        String event = "event";
        String advertiserId = "advertiser_id";
        String advertiserTrackingEnabled = "advertiser_tracking_enabled";
        String applicationTrackingEnabled = "application_tracking_enabled";
        String logTime = "_logTime";
        String eventName = "_eventName";
        String fbDescription = "fb_description";
        String fbContentId = "fb_content_id";
        String fbRegistrationMethod = "fb_registration_method";
        String fbContent = "fb_content";
        String fbContentType = "fb_content_type";
        String fbNumItems = "fb_num_items";
        String fbCurrency = "fb_currency";
        String _valueToSum = "_valueToSum";
        String customEvents = "custom_events";
        String appAccessToken = "app-access-token";
        String ud_externalId = "ud[external_id]";
    }

    interface AdjustConstant {
        String s2s = "s2s";
        String eventToken = "event_token";
        String appToken = "app_token";
        String idfa = "idfa";
        String gpsAdid = "gps_adid";
        String adid = "adid";
        String androidId = "android_id";
        String ipAddress = "ip_address";
        String createdAtUnix = "created_at_unix";
        String partnerParams = "partner_params";
        String callbackParams = "callback_params";
        String revenue = "revenue";
        String currency = "currency";
        String environment = "environment";

        //fire_adid
        //oaid
        //idfv

    }
    interface AppsFlyerConstant {

        String ipAddress = "ip_address";
        String createdAtUnix = "created_at_unix";
        String callbackParams = "callback_params";
        String revenue = "revenue";
        String currency = "currency";
        String environment = "environment";
        String idfa = "idfa";
        String odid = "odid";


        String appVersionName="app_version_name";
        String appsflyerId ="appsflyer_id";
        String advertisingId ="advertising_id";
        String customerUserId="customer_user_id";
        String os="os";
        String webDevKey="webDevKey";
        String eventType="eventType";
        String eventName="eventName";
        String eventTime="eventTime";
        String eventCurrency="eventCurrency";
        String eventRevenue="eventRevenue";
        String ip="ip";
        String eventValue="eventValue";
        String eventValue_afRevenue="af_revenue";
        String eventValue_afContentType="af_content_type";
        String eventValue_afContentId="af_content_id";
        String eventValue_afQuantity="af_quantity";
        //fire_adid
        //oaid
        //idfv

    }

    interface GoogleAnalyticsConstant {
        // token
        String apiSecret = "api_secret";
        String measurementId = "measurement_id";
        String firebaseAppId = "firebase_app_id";

        // device
        String deviceType = "device_type";
        String androidDeviceType = "android";
        String iosDeviceType = "ios";
        String webDeviceType = "web";
        String tabletDeviceType = "tablet";

        // deviceInfo
        String idfa = "idfa";
        String gid = "gid";

        // body
        String client_id = "client_id";
        String clientId = "clientId";
        String userId = "user_id";
        String timestampMicros = "timestamp_micros";

        // userProperties object
        String userProperties = "user_properties";

        String customerTier = "customer_tier";
        String customerTireValue = "value";
        String customerId = "customerId";
        String operation = "operation";

        // events array
        String events = "events";
        String name = "name";
        String params = "params";
    }

    /**
     * jsonObject body： 参数可选项
     */
    interface EventBodyConstant {
        /**
         * 入金
         */
        //浮点数金额
        String revenue = "revenue";
        //数字货币币种
        String currency = "currency";
        // 事件详细内容
        String body = "body";

        /**
         * facebook&adjust备用
         */
        String description = "description";
        String contentId = "contentId";
        String content = "content";
        String contentType = "contentType";
        String numItems = "numItems";
        //jsonObject
        String partnerParams = "partnerParams";
        //jsonObject
        String callbackParams = "callbackParams";
    }

    /**
     * 渠道配置项
     */
    interface FacebookConfig {
        String appId = "app-id";
        String apiUrl = "api_url";
        String appAccessToken = "app-access-token";
    }

    interface AdjustConfig {
        String apiUrl = "api_url";
        String environment = "environment";
        String iosAppToken = "ios_app_token";
        String androidAppToken = "android_app_token";
    }
    interface AppsFlyerConfig {
        String apiUrl = "api_url";
        String environment = "environment";
        String iosAppToken = "ios_app_token";
        String androidAppToken = "android_app_token";
    }

    /**
     * 阿里云风控渠道配置项
     */
    interface AliyunRiskConfig {
        String domain = "domain";
        String region = "region";
        String version = "version";
        String ak = "ak";
        String sk = "sk";
    }

    /**
     * GoogleAnalytics渠道配置项
     */
    interface GoogleAnalyticsConfig {
        String apiUrl = "api_url";
    }

    interface MugenConfig {
        String host = "host";
        String appKey = "app_key";
        String signature = "signature";
        String progressInfo = "progress_info";
        String customerId = "customer_id";
        String type = "type";
        String businessId = "business_id";
        String saasId = "saas_id";
    }
}
