package com.kikitrade.kevent.server;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 *
 *
 * <AUTHOR>
 * @create 2022/10/17 20:54
 * @modify
 */
public class SHPLClient {

    private static final String CHARSET_UTF8 = "utf8";
    private static final String ISO8601_DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ss'Z'";
    private final static String ALGORITHM = "UTF-8";
    private final static String SEPARATOR = "&";
    public static final String DECISION_SERVICE = "saf_de";

    /**
     * 获取token地址
     * token默认更新间隔时间，默认30分钟
     */
    private String tokenUrl;
    private final static int TOKEN_REFRESH_INTERVAL_TIME = 30 * 60 * 1000;

    private CloseableHttpClient client;
    private ShplToken shplToken;
    private String endpoint;

    private volatile static SHPLClient shplClient;

    private SHPLClient(String endpoint, String regionId, String accessKeyId, String accessKeySecret,
                       SHPLHttpClientConfig clientConfig) throws Exception {
        this.endpoint = endpoint;
        this.tokenUrl = "https://saf." + regionId + ".aliyuncs.com";
        init(accessKeyId, accessKeySecret, clientConfig);
    }

    public static SHPLClient getInstance(String endpoint, String regionId, String accessKeyId, String accessKeySecret,
                                         SHPLHttpClientConfig clientConfig) throws Exception {
        if (shplClient == null) {
            synchronized (SHPLClient.class) {
                if (shplClient == null) {
                    shplClient = new SHPLClient(endpoint, regionId, accessKeyId, accessKeySecret, clientConfig);
                }
            }
        }
        return shplClient;
    }

    /**
     * 初始化
     *
     * @param accessKeyId
     * @param accessKeySecret
     * @param clientConfig
     * @throws Exception
     */
    private void init(String accessKeyId, String accessKeySecret, SHPLHttpClientConfig clientConfig) throws Exception {

        // 初始化HttpClient线程池 https
        SSLConnectionSocketFactory trustSSLFactory = new SSLConnectionSocketFactory(new SSLContextBuilder().
                loadTrustMaterial(null, (TrustStrategy)(chain, authType) -> true).build(), NoopHostnameVerifier.INSTANCE);
        SSLConnectionSocketFactory sslFactory = SSLConnectionSocketFactory.getSocketFactory();
        SSLConnectionSocketFactory socketFactory = clientConfig.isIgnoreSSLCerts() ? trustSSLFactory : sslFactory;

        Registry<ConnectionSocketFactory> registry = RegistryBuilder.<ConnectionSocketFactory>create()
                .register("http", new PlainConnectionSocketFactory())
                .register("https", socketFactory).build();

        // 连接池
        PoolingHttpClientConnectionManager cm = new PoolingHttpClientConnectionManager(registry);
        // 最大连接数 每个路由基础的连接
        cm.setMaxTotal(clientConfig.getMaxRequests());
        cm.setDefaultMaxPerRoute(clientConfig.getMaxRequestsPerHost());

        // 设置超时时间
        RequestConfig requestConfig = RequestConfig.custom().setConnectionRequestTimeout(clientConfig.getConnectionRequestTimeout())
                .setConnectTimeout(clientConfig.getConnectTimeout()).setSocketTimeout(clientConfig.getSocketTimeout()).build();

        // 初始化Client
        client = HttpClients.custom().setSSLSocketFactory(socketFactory).setConnectionManager(cm)
                .setDefaultRequestConfig(requestConfig)
                .build();

        // 初始化Token并自动刷新
        initSHPLToken(accessKeyId, accessKeySecret);
        refreshSHPLTokenAuto(accessKeyId, accessKeySecret);
    }

    /**
     * 自动更新shplToken，避免过期
     *
     * @param accessKeyId
     * @param accessKeySecret
     */
    private void refreshSHPLTokenAuto(String accessKeyId, String accessKeySecret) {
        //自动刷新Token机制，避免过期
        new Timer().schedule(new TimerTask() {
            @Override
            public void run() {
                initSHPLToken(accessKeyId, accessKeySecret);
            }
        }, TOKEN_REFRESH_INTERVAL_TIME, TOKEN_REFRESH_INTERVAL_TIME);
    }

    /**
     * 第一次初始化shplToken
     *
     * @param accessKeyId
     * @param accessKeySecret
     * @return
     */
    private void initSHPLToken(String accessKeyId, String accessKeySecret) {
        Map<String, String> parameters = new HashMap(16);
        try {
            // 系统签名参数
            parameters.put("AccessKeyId", accessKeyId);
            parameters.put("Timestamp", formatIso8601Date(new Date()));
            parameters.put("SignatureMethod", "HMAC-SHA1");
            parameters.put("SignatureVersion", "1.0");
            parameters.put("SignatureNonce", UUID.randomUUID().toString());
            parameters.put("Format", "JSON");
            parameters.put("ServiceModule", "open_api");
            parameters.put("Action", "GetToken");
            parameters.put("Version", "2019-05-21");
            //签名参数
            parameters.put("Signature", generate("POST", parameters, accessKeySecret));
            this.shplToken = requestToken(tokenUrl, parameters);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * requestToken
     *
     * @param url
     * @param params
     * @return
     */
    private ShplToken requestToken(String url, Map<String, String> params) {

        ShplToken shplToken = new ShplToken();
        if (this.shplToken != null) {
            shplToken.setShplToken(this.shplToken.getShplToken());
            shplToken.setSecretKey(this.shplToken.getSecretKey());
        }
        CloseableHttpResponse response = null;
        try {
            HttpPost httpPost = new HttpPost(url);

            //装填参数
            List<NameValuePair> nameValuePairs = new ArrayList<>();
            if (params != null && !params.isEmpty()) {
                for (Map.Entry<String, String> entry : params.entrySet()) {
                    nameValuePairs.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
                }
            }
            //设置参数到请求对象中
            httpPost.setEntity(new UrlEncodedFormEntity(nameValuePairs, CHARSET_UTF8));

            response = client.execute(httpPost);
            int code = response.getStatusLine().getStatusCode();
            String content = EntityUtils.toString(response.getEntity());
            if (code == 200) {
                JSONObject restObj = JSONObject.parseObject(content);
                JSONObject data = restObj.getJSONObject("Data");
                if (data != null) {
                    shplToken.setShplToken(data.getString("token"));
                    shplToken.setSecretKey(data.getString("secretKey"));
                }
                shplToken.setErrMsg(null);
                shplToken.setRequestId(restObj.getString("RequestId"));
            } else {
                JSONObject contentObj = JSONObject.parseObject(content);
                shplToken.setErrMsg(contentObj.getString("Message"));
                shplToken.setRequestId(contentObj.getString("RequestId"));
                throw new RuntimeException("requestToken Exception! requestId: " + shplToken.getRequestId()
                        + " msg: " + shplToken.getErrMsg());
            }
            EntityUtils.consume(response.getEntity());
        } catch (Throwable e) {
            e.printStackTrace();
        } finally {
            try {
                response.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return shplToken;
    }

    /**
     * formatIso8601Date
     *
     * @param date
     * @return
     */
    private String formatIso8601Date(Date date) {
        SimpleDateFormat df = new SimpleDateFormat(ISO8601_DATE_FORMAT);
        df.setTimeZone(new SimpleTimeZone(0, "GMT"));
        return df.format(date);
    }

    /**
     * generateQueryString
     *
     * @param params
     * @param isEncodeKV
     * @return
     */
    private String generateQueryString(Map<String, String> params, boolean isEncodeKV) {
        StringBuilder canonicalizedQueryString = new StringBuilder();
        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (isEncodeKV) {
                canonicalizedQueryString.append(percentEncode(entry.getKey())).append("=")
                        .append(percentEncode(entry.getValue())).append("&");
            } else {
                canonicalizedQueryString.append(entry.getKey()).append("=")
                        .append(entry.getValue()).append("&");
            }
        }
        if (canonicalizedQueryString.length() > 1) {
            canonicalizedQueryString.setLength(canonicalizedQueryString.length() - 1);
        }
        return canonicalizedQueryString.toString();
    }

    /**
     * percentEncode
     *
     * @param value
     * @return
     */
    private String percentEncode(String value) {
        try {
            // 使用URLEncoder.encode编码后，将"+","*","%7E"做替换即满足 API规定的编码规范
            return value == null ? null : URLEncoder.encode(value, CHARSET_UTF8)
                    .replace("+", "%20").replace("*", "%2A").replace("%7E", "~");
        } catch (Exception e) {
            //不可能发生的异常
        }
        return "";
    }

    /**
     * generate
     *
     * @param parameter
     * @param accessKeySecret
     * @return
     * @throws Exception
     */
    private String simpleGenerate(Map<String, String> parameter, String accessKeySecret) throws Exception {
        String signString = simpleGenerateSignString(parameter);
        byte[] signBytes = hmacSHA1Signature(accessKeySecret + "&", signString);
        return newStringByBase64(signBytes);
    }

    /**
     * simpleGenerateSignString
     *
     * @param parameter
     * @return
     * @throws IOException
     */
    private String simpleGenerateSignString(Map<String, String> parameter) {
        TreeMap<String, String> sortParameter = new TreeMap<>();
        sortParameter.putAll(parameter);
        return generateQueryString(sortParameter, false);
    }

    /**
     * generate
     *
     * @param method
     * @param parameter
     * @param accessKeySecret
     * @return
     * @throws Exception
     */
    private String generate(String method, Map<String, String> parameter, String accessKeySecret) throws Exception {
        String signString = generateSignString(method, parameter);
        byte[] signBytes = hmacSHA1Signature(accessKeySecret + "&", signString);
        String signature = newStringByBase64(signBytes);
        if ("POST".equals(method)) {return signature;}
        return URLEncoder.encode(signature, "UTF-8");
    }

    /**
     * generateSignString
     *
     * @param httpMethod
     * @param parameter
     * @return
     * @throws IOException
     */
    private String generateSignString(String httpMethod, Map<String, String> parameter) {
        TreeMap<String, String> sortParameter = new TreeMap<>();
        sortParameter.putAll(parameter);
        String canonicalizedQueryString = generateQueryString(sortParameter, true);
        if (null == httpMethod) {
            throw new RuntimeException("httpMethod can not be empty");
        }
        StringBuilder stringToSign = new StringBuilder();
        stringToSign.append(httpMethod).append(SEPARATOR);
        stringToSign.append(percentEncode("/")).append(SEPARATOR);
        stringToSign.append(percentEncode(canonicalizedQueryString));
        return stringToSign.toString();
    }

    /**
     * hmacSHA1Signature
     *
     * @param secret
     * @param baseString
     * @return
     * @throws Exception
     */
    private byte[] hmacSHA1Signature(String secret, String baseString)
            throws Exception {
        if (StringUtils.isEmpty(secret)) {
            throw new IOException("secret can not be empty");
        }
        if (StringUtils.isEmpty(baseString)) {
            return null;
        }
        Mac mac = Mac.getInstance("HmacSHA1");
        SecretKeySpec keySpec = new SecretKeySpec(secret.getBytes(CHARSET_UTF8), ALGORITHM);
        mac.init(keySpec);
        return mac.doFinal(baseString.getBytes(CHARSET_UTF8));
    }

    /**
     * newStringByBase64
     *
     * @param bytes
     * @return
     * @throws UnsupportedEncodingException
     */
    private String newStringByBase64(byte[] bytes)
            throws UnsupportedEncodingException {
        if (bytes == null || bytes.length == 0) {
            return null;
        }
        return new String(Base64.encodeBase64(bytes, false), CHARSET_UTF8);
    }

    /**
     * 调用服务
     *
     * @param shplRequest
     * @return
     */
    public SHPLResponse getSHPLResponse(SHPLRequest shplRequest) {
        SHPLResponse shplResponse;
        CloseableHttpResponse response = null;

        try {
            SHPLResponse fixErrorResponse = getPopFixErrorRes(shplToken);
            if (fixErrorResponse != null) {
                return fixErrorResponse;
            }

            Map<String, String> parameters = new HashMap(16);
            // 系统参数
            parameters.put("Timestamp", String.valueOf(System.currentTimeMillis()));
            parameters.put("Token", shplToken.getShplToken());
            parameters.put("Service", shplRequest.getService());
            // 业务参数
            parameters.put("ServiceParameters", JSONObject.toJSONString(shplRequest.getServiceParameters()));
            // 签名
            parameters.put("Signature", simpleGenerate(parameters, shplToken.getSecretKey()));
            parameters.put("EventCode", shplRequest.getEventCode());

            String path = DECISION_SERVICE.equals(shplRequest.getService()) ? "/requestDecisionByToken.do" : "/requestPublicByToken.do";

            HttpPost httpPost = new HttpPost(endpoint + path);

            //装填参数
            List<NameValuePair> nameValuePairs = new ArrayList<>();
            if (parameters != null && !parameters.isEmpty()) {
                for (Map.Entry<String, String> entry : parameters.entrySet()) {
                    nameValuePairs.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
                }
            }
            //设置参数到请求对象中
            httpPost.setEntity(new UrlEncodedFormEntity(nameValuePairs, CHARSET_UTF8));

            //System.out.println("parameters:" + JSONObject.toJSONString(parameters) + " getTokenRequestId：" + shplToken.getRequestId());

            response = client.execute(httpPost);
            int code = response.getStatusLine().getStatusCode();
            String content = EntityUtils.toString(response.getEntity());
            if (code != 200) {
                throw new RuntimeException("getContent error,code=" + code + ",content=" + content);
            }
            shplResponse = JSONObject.parseObject(content, SHPLResponse.class);
            EntityUtils.consume(response.getEntity());
        } catch (Throwable e) {
            shplResponse = getErrorRes(500, e.getMessage(), null);
            e.printStackTrace();
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return shplResponse;
    }

    private SHPLResponse getPopFixErrorRes(ShplToken shplToken) {
        SHPLResponse response = null;
        if (shplToken != null && shplToken.getErrMsg() != null && shplToken.getShplToken() == null) {
            response = new SHPLResponse();
            response.setCode(404);
            response.setMessage(shplToken.getErrMsg());
            response.setRequestId(shplToken.getRequestId());
            return response;
        }
        if (shplToken == null || shplToken.getShplToken() == null || shplToken.getSecretKey() == null) {
            response = new SHPLResponse();
            response.setCode(400);
            response.setMessage("shplToken is Empty! ");
            response.setRequestId(shplToken.getRequestId());
            return response;
        }
        return null;
    }

    private SHPLResponse getErrorRes(Integer code, String msg, String requestId) {
        SHPLResponse response = new SHPLResponse();
        response.setCode(code);
        response.setMessage(msg);
        response.setRequestId(requestId);
        return response;
    }

    public static class SHPLRequest {

        private String service;
        private String eventCode;
        private Map<String, Object> serviceParameters;

        public String getService() {
            return service;
        }

        public void setService(String service) {
            this.service = service;
        }

        public String getEventCode() {
            return eventCode;
        }

        public void setEventCode(String eventCode) {
            this.eventCode = eventCode;
        }

        public Map<String, Object> getServiceParameters() {
            return serviceParameters;
        }

        public void setServiceParameters(Map<String, Object> serviceParameters) {
            this.serviceParameters = serviceParameters;
        }

    }

    public static class SHPLHttpClientConfig {

        private boolean ignoreSSLCerts = false;

        private int connectionRequestTimeout = 5000;
        private int connectTimeout = 5000;
        private int socketTimeout = 5000;

        private int maxRequests = 400;
        private int maxRequestsPerHost = 200;

        public boolean isIgnoreSSLCerts() {
            return ignoreSSLCerts;
        }

        public void setIgnoreSSLCerts(boolean ignoreSSLCerts) {
            this.ignoreSSLCerts = ignoreSSLCerts;
        }

        public int getConnectionRequestTimeout() {
            return connectionRequestTimeout;
        }

        public void setConnectionRequestTimeout(int connectionRequestTimeout) {
            this.connectionRequestTimeout = connectionRequestTimeout;
        }

        public int getConnectTimeout() {
            return connectTimeout;
        }

        public void setConnectTimeout(int connectTimeout) {
            this.connectTimeout = connectTimeout;
        }

        public int getSocketTimeout() {
            return socketTimeout;
        }

        public void setSocketTimeout(int socketTimeout) {
            this.socketTimeout = socketTimeout;
        }

        public int getMaxRequests() {
            return maxRequests;
        }

        public void setMaxRequests(int maxRequests) {
            this.maxRequests = maxRequests;
        }

        public int getMaxRequestsPerHost() {
            return maxRequestsPerHost;
        }

        public void setMaxRequestsPerHost(int maxRequestsPerHost) {
            this.maxRequestsPerHost = maxRequestsPerHost;
        }
    }

    public static class ShplToken {
        private String shplToken;
        private String secretKey;
        private String errMsg;
        private String requestId;

        public String getShplToken() {
            return shplToken;
        }

        public void setShplToken(String shplToken) {
            this.shplToken = shplToken;
        }

        public String getSecretKey() {
            return secretKey;
        }

        public void setSecretKey(String secretKey) {
            this.secretKey = secretKey;
        }

        public String getErrMsg() {
            return errMsg;
        }

        public void setErrMsg(String errMsg) {
            this.errMsg = errMsg;
        }

        public String getRequestId() {
            return requestId;
        }

        public void setRequestId(String requestId) {
            this.requestId = requestId;
        }
    }

    public static class SHPLResponse {
        private Integer code;
        private String message;
        private Map<Object, Object> data;
        private String requestId;

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public Map<Object, Object> getData() {
            return data;
        }

        public void setData(Map<Object, Object> data) {
            this.data = data;
        }

        public String getRequestId() {
            return requestId;
        }

        public void setRequestId(String requestId) {
            this.requestId = requestId;
        }
    }

}
