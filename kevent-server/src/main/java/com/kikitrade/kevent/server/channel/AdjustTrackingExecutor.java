package com.kikitrade.kevent.server.channel;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kikitrade.kcustomer.api.model.DeviceInfoDTO;
import com.kikitrade.kevent.common.constant.EventConstants;
import com.kikitrade.kevent.common.constant.EventConstants.AdjustConfig;
import com.kikitrade.kevent.common.constant.EventConstants.AdjustConstant;
import com.kikitrade.kevent.common.constant.EventConstants.DeliveryStatus;
import com.kikitrade.kevent.common.constant.EventConstants.EventBodyConstant;
import com.kikitrade.kevent.common.exception.EventException;
import com.kikitrade.kevent.dal.builder.EventStoreBuilder;
import com.kikitrade.kevent.dal.model.EventDO;
import com.kikitrade.kevent.server.autoconfigure.KEventProperties;
import com.kikitrade.kevent.server.executor.impl.TrackingExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import jakarta.annotation.Resource;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import static com.kikitrade.kevent.common.constant.EventMessageEnum.SYSTEM_PARAMETER_REQUIRED;

/**
 * @author: penuel
 * @date: 2022/5/5 15:00
 * @desc: TODO
 */
@Component("adjustTrackingExecutor")
@Slf4j
@ConditionalOnProperty(name = {"kevent.adjust.api_url"})
public class AdjustTrackingExecutor extends TrackingExecutor {

    @Resource
    private KEventProperties kEventProperties;

    /**
     * 设备OS类型枚举
     */
    String androidDeviceType = "android";
    String iosDeviceType = "ios";
    /**
     * 线上环境变量
     */
    String prod = "prod";

    @Autowired
    public EventStoreBuilder eventStoreBuilder;
    /**
     * 同步执行修复数据
     * @param input
     */
    public DeliveryStatus executeFix(EventDO input) {
        eventStoreBuilder.updateStatusByType(input.getId(), deliveryChannel(), EventConstants.DeliveryStatus.PROCESSING);
        EventConstants.DeliveryStatus deliveryStatus = doExecute(input);
        eventStoreBuilder.updateStatusByType(input.getId(), deliveryChannel(), deliveryStatus);
        return deliveryStatus ;
    }
    @Override
    public boolean isContinue(EventDO input) {
        boolean result = super.isContinue(input);
        if (result) {
            Set<String> keySet = kEventProperties.getAdjustEventToken().keySet();
            if (keySet.parallelStream().anyMatch(k -> regularCheck(k, parseOriginalName(input.getName()).getFirst()))) {
                return true;
            }
            return false;
        }
        return false;
    }

    @Override
    public Map<String, Object> mapping(EventDO event) throws EventException {
        //查询设备信息
        DeviceInfoDTO deviceInfo = getDeviceInfoByCustomerIdOrDeviceId(event.getCustomerId(),event.getDeviceId());
        //校验并设置参数
        MultiValueMap<String, Object> postParameters = new LinkedMultiValueMap<>();
        String environment = "sandbox";
        if (prod.equals(kEventProperties.getEnv())) {
            environment = "production";
        }
        postParameters.add(AdjustConstant.environment, environment);
        postParameters.add(AdjustConstant.s2s, 1);

        //原始事件名称
        String preName = parseOriginalName(event.getName()).getFirst();
        //后缀
        String suffix = parseOriginalName(event.getName()).getSecond();
        String eventToken = "";
        if (iosDeviceType.equals(deviceInfo.getDeviceType())) {
            eventToken = getAdjustEventToken().get("ios__" + preName + "__" + suffix);
        } else {
            eventToken = getAdjustEventToken().get("android__" + preName + "__" + suffix);
        }
        paramCheck(eventToken);
        postParameters.add(AdjustConstant.eventToken, eventToken);

        //根据设备os传对应的app_token
        if (iosDeviceType.equals(deviceInfo.getDeviceType())) {
            postParameters.add(AdjustConstant.appToken, getAdjustConfig().get(AdjustConfig.iosAppToken));
        } else {
            postParameters.add(AdjustConstant.appToken, getAdjustConfig().get(AdjustConfig.androidAppToken));
        }

        String idfa = "";
        //必传参数
        if (iosDeviceType.equals(deviceInfo.getDeviceType()) && StringUtils.isNotBlank(deviceInfo.getDeviceId())) {
            idfa = getOriginalDeviceId(deviceInfo.getDeviceId());
            postParameters.add(AdjustConstant.idfa, idfa);
        }
        String gpsAdid = "";
        if (androidDeviceType.equals(deviceInfo.getDeviceType()) && StringUtils.isNotBlank(deviceInfo.getDeviceId())) {
            gpsAdid = getOriginalDeviceId(deviceInfo.getDeviceId());
            postParameters.add(AdjustConstant.gpsAdid, gpsAdid);
        }
        if (StringUtils.isNotBlank(deviceInfo.getAdjustDeviceId())) {
            postParameters.add(AdjustConstant.adid, deviceInfo.getAdjustDeviceId());
        }
        //安卓ID 是无效的先不传
//        if (androidDeviceType.equals(deviceInfo.getDeviceType()) && StringUtils.isNotBlank(deviceInfo.getAdId())) {
//            postParameters.add(AdjustConstant.androidId, deviceInfo.getAdId());
//        }
        if (StringUtils.isBlank(idfa)
                && StringUtils.isBlank(gpsAdid)
                && StringUtils.isBlank(deviceInfo.getAdjustDeviceId())
                && StringUtils.isBlank(deviceInfo.getAdId())) {
            throw new EventException(SYSTEM_PARAMETER_REQUIRED);
        }
        //建议参数
        if (StringUtils.isNotBlank(deviceInfo.getIp())) {
            postParameters.add(AdjustConstant.ipAddress, deviceInfo.getIp());
        }
        if (Objects.nonNull(event.getTime())) {
            postParameters.add(AdjustConstant.createdAtUnix, event.getTime());
        }

        JSONObject eventBody = JSONObject.parseObject(event.getBody());
        //合作伙伴参数
//        paramCheckAndSet(postParameters, eventBody, AdjustConstant.partnerParams, EventBodyConstant.partnerParams);

        //如果是入金，将对应金额转为法币
        if (Objects.nonNull(eventBody)
                && Objects.nonNull(eventBody.get(EventBodyConstant.revenue))
                && Objects.nonNull(eventBody.get(EventBodyConstant.currency))) {
            String revenue = String.valueOf(eventBody.get(EventBodyConstant.revenue));
            String currency = String.valueOf(eventBody.get(EventBodyConstant.currency));

            String fiatRevenue = calFiatCurrency(revenue, currency);

            //跟踪收入参数
            postParameters.add(AdjustConstant.currency, fiatCurrency);
            postParameters.add(AdjustConstant.revenue, fiatRevenue);

            JSONObject jo = new JSONObject();
            jo.put(EventBodyConstant.currency, currency);
            jo.put(EventBodyConstant.revenue, revenue);
            //回传参数
            postParameters.add(AdjustConstant.callbackParams, jo.toJSONString());
        }
        log.info("adjust postParameters:{}", JSON.toJSONString(postParameters));
        return (Map) postParameters;
    }

    @Override
    public EventConstants.DeliveryStatus track(Map<String, Object> params) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/x-www-form-urlencoded");
//        headers.add("Authorization", "Bearer v8s5opi0zs45607l8h8c48wak2vxb5oe");
        try {
            return apiCall(HttpMethod.POST, EventConstants.DeliveryStatus.class, params, headers);
        } catch (Exception e) {
            log.warn("Adjust track data error", e);
            return DeliveryStatus.FAIL;
        }
    }

    @Override
    public String endpoint() {
        return getAdjustConfig().get(AdjustConfig.apiUrl);
    }

    @Override
    public EventConstants.DeliveryChannel deliveryChannel() {
        return EventConstants.DeliveryChannel.ADJUST;
    }

    Map<String, String> getAdjustConfig() {
        return kEventProperties.getAdjust();
    }

    Map<String, String> getAdjustEventToken() {
        return kEventProperties.getAdjustEventToken();
    }

    public static void main(String[] args) {
        String kyc_first = "kyc_first";
        String preName = kyc_first.substring(0, kyc_first.lastIndexOf("_first"));
        System.out.println(preName);

    }
}


