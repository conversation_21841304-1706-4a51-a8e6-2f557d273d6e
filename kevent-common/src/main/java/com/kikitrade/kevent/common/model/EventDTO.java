package com.kikitrade.kevent.common.model;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * @author: penuel
 * @date: 2022/5/4 12:15
 * @desc: TODO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EventDTO implements Serializable {

    /**
     * 事件名称 @EventConstants.EventName
     */
    private String name;
    /**
     * 事件发生时间
     */
    private Long time;
    /**
     * 必填项用户ID
     */
    private String customerId;

    /**
     * 用户设备ID
     */
    private String deviceId;
    /**
     * 非必须 金额 -//浮点数金额
     */
    private BigDecimal amount;
    /**
     * //非必须 -数字货币币种
     */
    private String currency;
    /**
     * 非必须 价格币种当前价格
     */
    private String price;
    /**
     * 非必须 - 全局事件幂等性唯一id
     * eg. orderId->用户入金事件
     */
    private String globalUid;
    /**
     * 非必须 - 首次事件唯一id
     * eg. customerId->用户首次入金事件
     */
    private String firstUid;
    /**
     * 自动适配 not required/默认获取 spring.application.name/id
     */
    private String source;
    /**
     * 设备token
     */
    private String deviceToken;
    /**
     * 设置ip
     */
    private String ip;
    /**
     * 事件体内容 - jsonString
     */
    private JSONObject body;

    /**
     * 定制化ons蓝绿tag
     */
    private String onsTag;
}
